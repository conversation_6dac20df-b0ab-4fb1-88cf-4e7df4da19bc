# Bible Companion Backend

FastAPI backend for the Bible Companion app with Gemma 2B chat integration, MongoDB storage, and Firebase authentication.

## Features

- 🤖 **AI Chat**: Gemma 2B model integration via TGI
- 🔐 **Authentication**: Firebase JWT validation
- 📚 **Context-Aware**: Uses user's reading history for relevant responses
- 🗄️ **Dual Storage**: MongoDB for app data, Neo4j for verse relationships
- ⚡ **Rate Limited**: 60 requests/minute per user
- 🧪 **Well Tested**: Comprehensive unit tests with mocked dependencies
- 📊 **Monitoring**: Structured logging and health checks

## Quick Start

### 1. Prerequisites

- Python 3.9+
- MongoDB (local or Atlas)
- Neo4j database
- TGI server running Gemma model
- Firebase project with service account

### 2. Installation

```bash
# Clone repository
git clone https://github.com/Jpkay/bible.git
cd bible/backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

Required environment variables:

```env
# Application
SECRET_KEY=your-secret-key-here
MONGODB_URI=mongodb://localhost:27017
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-password

# TGI
TGI_BASE_URL=http://localhost:8080

# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CREDENTIALS_PATH=path/to/service-account.json
```

### 4. Start TGI Server

```bash
# Navigate to TGI directory
cd ../infra/tgi

# Start with Docker Compose
docker compose up -d

# Verify TGI is running
curl http://localhost:8080/health
```

### 5. Run Backend

```bash
# Development server
uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000

# Production server
uvicorn backend.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 6. Verify Installation

```bash
# Health check
curl http://localhost:8000/health

# Detailed health check
curl http://localhost:8000/health/detailed
```

## API Documentation

Once running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

Or see the [Chat API Documentation](../docs/chat.md) for detailed endpoint information.

## Testing

### Run Tests

```bash
# Install test dependencies
pip install -e ".[test]"
# Run all tests
pytest

# Run with coverage
pytest --cov=backend --cov-report=html

# Run specific test file
pytest backend/tests/test_chat_api.py -v
```

### Test Structure

```
backend/tests/
├── conftest.py           # Test configuration and fixtures
├── test_chat_api.py      # Chat endpoint tests
├── test_tgi_client.py    # TGI client tests
└── test_auth.py          # Authentication tests
```

## Development

### Code Quality

```bash
# Format code
black backend/
isort backend/

# Lint code
flake8 backend/

# Type checking
mypy backend/
```

### Project Structure

```
backend/
├── core/                 # Core functionality
│   ├── auth.py          # Firebase authentication
│   ├── config.py        # Configuration settings
│   ├── database.py      # Database connections
│   └── exceptions.py    # Custom exceptions
├── models/              # Pydantic models
│   └── chat.py         # Chat-related models
├── routers/             # FastAPI routers
│   ├── chat.py         # Chat endpoints
│   └── health.py       # Health check endpoints
├── services/            # Business logic
│   ├── chat_service.py # Chat conversation management
│   └── tgi_client.py   # TGI integration
├── tests/               # Unit tests
└── main.py             # FastAPI application
```

## Deployment

### Docker

```bash
# Build image
docker build -t bible-companion-backend .

# Run container
docker run -p 8000:8000 --env-file .env bible-companion-backend
```

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `SECRET_KEY` | JWT secret key | Yes |
| `MONGODB_URI` | MongoDB connection string | Yes |
| `NEO4J_URI` | Neo4j connection URI | Yes |
| `NEO4J_USERNAME` | Neo4j username | Yes |
| `NEO4J_PASSWORD` | Neo4j password | Yes |
| `TGI_BASE_URL` | TGI server URL | Yes |
| `FIREBASE_PROJECT_ID` | Firebase project ID | Yes |
| `FIREBASE_CREDENTIALS_PATH` | Service account JSON path | Yes |
| `DEBUG` | Enable debug mode | No |
| `RATE_LIMIT_PER_MINUTE` | Rate limit per user | No |

## Monitoring

### Health Checks

- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed dependency status
- `GET /health/ready` - Kubernetes readiness probe
- `GET /health/live` - Kubernetes liveness probe

### Logging

The application uses structured logging with the following levels:
- `INFO`: Normal operations
- `WARNING`: Potential issues
- `ERROR`: Error conditions
- `DEBUG`: Detailed debugging (dev only)

### Metrics

Prometheus metrics are available at `/metrics` (when enabled).

## Troubleshooting

### Common Issues

1. **TGI Connection Failed**
   ```bash
   # Check TGI server status
   curl http://localhost:8080/health

   # Check TGI logs
   docker compose logs tgi
   ```

2. **MongoDB Connection Error**
   ```bash
   # Test MongoDB connection
   mongosh "mongodb://localhost:27017"
   ```

3. **Firebase Authentication Error**
   ```bash
   # Verify service account file
   cat path/to/service-account.json

   # Check Firebase project ID
   echo $FIREBASE_PROJECT_ID
   ```

4. **Rate Limiting Issues**
   ```bash
   # Check rate limit configuration
   grep RATE_LIMIT .env
   ```

### Debug Mode

Enable debug mode for detailed logging:

```env
DEBUG=true
LOG_LEVEL=DEBUG
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run quality checks
5. Submit a pull request

### Code Standards

- Follow PEP 8 style guide
- Add type hints to all functions
- Write docstrings for public APIs
- Include unit tests for new features
- Use structured logging

## License

This project is licensed under the MIT License - see the LICENSE file for details.
