"""
Tests for TGI client.
"""

from unittest.mock import AsyncMock, patch

import httpx
import pytest

from backend.core.exceptions import TGI<PERSON>rror
from backend.services.tgi_client import TGIClient


class TestTGIClient:
    """Test TGI client functionality."""
    
    @pytest.mark.asyncio
    async def test_health_check_success(self):
        """Test successful health check."""
        
        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            # Mock successful health check response
            mock_response = AsyncMock()
            mock_response.status_code = 200
            mock_client.get.return_value = mock_response
            
            tgi_client = TGIClient()
            tgi_client.client = mock_client
            
            result = await tgi_client.health_check()
            
            assert result is True
            mock_client.get.assert_called_once_with("http://localhost:8080/health")
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self):
        """Test failed health check."""
        
        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            # Mock failed health check response
            mock_response = AsyncMock()
            mock_response.status_code = 500
            mock_client.get.return_value = mock_response
            
            tgi_client = TGIClient()
            tgi_client.client = mock_client
            
            result = await tgi_client.health_check()
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_generate_text_success(self):
        """Test successful text generation."""

        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client

            # Mock successful generation response
            mock_response = AsyncMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "generated_text": "This is a generated response."
            }
            mock_client.post.return_value = mock_response

            tgi_client = TGIClient()
            tgi_client.client = mock_client

            result = await tgi_client.generate_text(
                prompt="What is faith?",
                max_new_tokens=100,
                temperature=0.7
            )

            assert result == "This is a generated response."
            mock_client.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_text_list_response(self):
        """Test text generation with list response format."""

        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client

            # Mock list response format
            mock_response = AsyncMock()
            mock_response.status_code = 200
            mock_response.json.return_value = [
                {"generated_text": "First response."},
                {"generated_text": "Second response."}
            ]
            mock_client.post.return_value = mock_response

            tgi_client = TGIClient()
            tgi_client.client = mock_client

            result = await tgi_client.generate_text(prompt="Test prompt")

            assert result == "First response."
    
    @pytest.mark.asyncio
    async def test_generate_text_http_error(self):
        """Test text generation with HTTP error."""

        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client

            # Mock HTTP error response
            mock_response = AsyncMock()
            mock_response.status_code = 500
            mock_response.json.return_value = {"error": "Internal server error"}
            mock_response.text = "Internal server error"
            mock_client.post.return_value = mock_response

            tgi_client = TGIClient()
            tgi_client.client = mock_client

            # Disable retry for testing by patching the method directly
            import types
            original_method = tgi_client.generate_text.__wrapped__
            tgi_client.generate_text = types.MethodType(original_method, tgi_client)

            with pytest.raises(TGIError) as exc_info:
                await tgi_client.generate_text(prompt="Test prompt")

            assert "TGI request failed with status 500" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_generate_text_timeout(self):
        """Test text generation with timeout."""

        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client

            # Mock timeout exception
            mock_client.post.side_effect = httpx.TimeoutException("Request timed out")

            tgi_client = TGIClient()
            tgi_client.client = mock_client

            # Disable retry for testing by patching the method directly
            import types
            original_method = tgi_client.generate_text.__wrapped__
            tgi_client.generate_text = types.MethodType(original_method, tgi_client)

            with pytest.raises(TGIError) as exc_info:
                await tgi_client.generate_text(prompt="Test prompt")

            assert "timed out" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_generate_text_connection_error(self):
        """Test text generation with connection error."""

        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client

            # Mock connection error
            mock_client.post.side_effect = httpx.ConnectError("Connection failed")

            tgi_client = TGIClient()
            tgi_client.client = mock_client

            # Disable retry for testing by patching the method directly
            import types
            original_method = tgi_client.generate_text.__wrapped__
            tgi_client.generate_text = types.MethodType(original_method, tgi_client)

            with pytest.raises(TGIError) as exc_info:
                await tgi_client.generate_text(prompt="Test prompt")

            assert "Failed to connect to TGI server" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_generate_text_empty_response(self):
        """Test text generation with empty response."""

        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client

            # Mock empty response
            mock_response = AsyncMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"generated_text": ""}
            mock_client.post.return_value = mock_response

            tgi_client = TGIClient()
            tgi_client.client = mock_client

            # Disable retry for testing by patching the method directly
            import types
            original_method = tgi_client.generate_text.__wrapped__
            tgi_client.generate_text = types.MethodType(original_method, tgi_client)

            with pytest.raises(TGIError) as exc_info:
                await tgi_client.generate_text(prompt="Test prompt")

            assert "Empty response from TGI server" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_model_info_success(self):
        """Test successful model info retrieval."""

        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client

            # Mock model info response
            mock_response = AsyncMock()
            mock_response.json.return_value = {
                "model_id": "google/gemma-2b-it",
                "model_dtype": "float16",
                "model_device_type": "cuda"
            }
            mock_response.raise_for_status.return_value = None
            mock_client.get.return_value = mock_response

            tgi_client = TGIClient()
            tgi_client.client = mock_client

            result = await tgi_client.get_model_info()

            assert result["model_id"] == "google/gemma-2b-it"
            mock_client.get.assert_called_once_with("http://localhost:8080/info")
    
    @pytest.mark.asyncio
    async def test_get_model_info_error(self):
        """Test model info retrieval error."""
        
        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            # Mock error
            mock_client.get.side_effect = httpx.HTTPStatusError(
                "Not found", request=None, response=None
            )
            
            tgi_client = TGIClient()
            tgi_client.client = mock_client
            
            with pytest.raises(TGIError) as exc_info:
                await tgi_client.get_model_info()
            
            assert "Failed to get model info" in str(exc_info.value)
