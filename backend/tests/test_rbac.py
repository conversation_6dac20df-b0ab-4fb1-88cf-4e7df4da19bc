"""
Test RBAC (Role-Based Access Control) for Bible Companion Neo4j isolation.

This test verifies that the bc_app user can only access BC_* labeled nodes
and is properly denied access to AiLex_* or other tenant data.
"""

import os
import pytest
from neo4j import GraphDatabase
from neo4j.exceptions import ClientError, Forbidden


class TestBibleCompanionRBAC:
    """Test RBAC isolation for Bible Companion."""
    
    @pytest.fixture(scope="class")
    def neo4j_driver(self):
        """Create Neo4j driver for testing."""
        uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
        username = os.getenv("NEO4J_USER", "bc_app")
        password = os.getenv("NEO4J_PASSWORD", "ChangeMe123!")
        
        driver = GraphDatabase.driver(uri, auth=(username, password))
        yield driver
        driver.close()
    
    @pytest.fixture(scope="class")
    def admin_driver(self):
        """Create admin Neo4j driver for setup/teardown."""
        uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
        # Use admin credentials for setup
        username = os.getenv("NEO4J_ADMIN_USER", "neo4j")
        password = os.getenv("NEO4J_ADMIN_PASSWORD", "password")
        
        driver = GraphDatabase.driver(uri, auth=(username, password))
        yield driver
        driver.close()
    
    @pytest.fixture(scope="class")
    def setup_test_data(self, admin_driver):
        """Setup test data with both BC_ and AiLex_ nodes."""
        with admin_driver.session(database="neo4j") as session:
            # Create test BC_ nodes
            session.run("""
                MERGE (u:BC_User {id: 'test_user_rbac', email: '<EMAIL>'})
                MERGE (v:BC_Verse {
                    id: 'test_verse_rbac',
                    lang: 'en', ver: 'NIV', book: 'John', chapter: 3, verse: 16,
                    text: 'For God so loved the world...'
                })
                MERGE (c:BC_Circle {id: 'test_circle_rbac', name: 'Test Circle'})
            """)
            
            # Create test AiLex_ nodes (simulating other tenant)
            session.run("""
                MERGE (d:AiLex_Doc {id: 'test_doc_rbac', title: 'Secret Document'})
                MERGE (k:AiLex_Knowledge {id: 'test_knowledge_rbac', content: 'Secret Knowledge'})
            """)
        
        yield
        
        # Cleanup test data
        with admin_driver.session(database="neo4j") as session:
            session.run("""
                MATCH (n) 
                WHERE n.id IN ['test_user_rbac', 'test_verse_rbac', 'test_circle_rbac', 
                              'test_doc_rbac', 'test_knowledge_rbac']
                DETACH DELETE n
            """)
    
    def test_bc_app_can_read_bc_nodes(self, neo4j_driver, setup_test_data):
        """Test that bc_app user can read BC_* labeled nodes."""
        with neo4j_driver.session(database="neo4j") as session:
            # Should be able to read BC_User nodes
            result = session.run("MATCH (u:BC_User) RETURN count(u) AS count")
            count = result.single()["count"]
            assert count >= 0  # Should not raise an exception
            
            # Should be able to read BC_Verse nodes
            result = session.run("MATCH (v:BC_Verse) RETURN count(v) AS count")
            count = result.single()["count"]
            assert count >= 0
            
            # Should be able to read BC_Circle nodes
            result = session.run("MATCH (c:BC_Circle) RETURN count(c) AS count")
            count = result.single()["count"]
            assert count >= 0
    
    def test_bc_app_can_write_bc_nodes(self, neo4j_driver, setup_test_data):
        """Test that bc_app user can write to BC_* labeled nodes."""
        with neo4j_driver.session(database="neo4j") as session:
            # Should be able to create BC_User nodes
            result = session.run("""
                CREATE (u:BC_User {
                    id: 'test_write_user', 
                    email: '<EMAIL>',
                    createdAt: datetime()
                })
                RETURN u.id as id
            """)
            user_id = result.single()["id"]
            assert user_id == "test_write_user"
            
            # Should be able to update BC_User nodes
            session.run("""
                MATCH (u:BC_User {id: 'test_write_user'})
                SET u.updatedAt = datetime()
            """)
            
            # Cleanup
            session.run("MATCH (u:BC_User {id: 'test_write_user'}) DELETE u")
    
    def test_bc_app_cannot_read_ailex_nodes(self, neo4j_driver, setup_test_data):
        """Test that bc_app user cannot read AiLex_* labeled nodes."""
        with neo4j_driver.session(database="neo4j") as session:
            # Should NOT be able to read AiLex_Doc nodes
            with pytest.raises((ClientError, Forbidden)):
                session.run("MATCH (d:AiLex_Doc) RETURN d").consume()
            
            # Should NOT be able to read AiLex_Knowledge nodes
            with pytest.raises((ClientError, Forbidden)):
                session.run("MATCH (k:AiLex_Knowledge) RETURN k").consume()
    
    def test_bc_app_cannot_write_ailex_nodes(self, neo4j_driver, setup_test_data):
        """Test that bc_app user cannot write AiLex_* labeled nodes."""
        with neo4j_driver.session(database="neo4j") as session:
            # Should NOT be able to create AiLex_Doc nodes
            with pytest.raises((ClientError, Forbidden)):
                session.run("""
                    CREATE (d:AiLex_Doc {id: 'forbidden_doc', title: 'Should Fail'})
                """).consume()
            
            # Should NOT be able to update AiLex_Knowledge nodes
            with pytest.raises((ClientError, Forbidden)):
                session.run("""
                    MATCH (k:AiLex_Knowledge {id: 'test_knowledge_rbac'})
                    SET k.modified = datetime()
                """).consume()
    
    def test_bc_app_can_use_apoc_functions(self, neo4j_driver):
        """Test that bc_app user can use APOC functions needed for the application."""
        with neo4j_driver.session(database="neo4j") as session:
            # Should be able to use APOC date functions
            result = session.run("RETURN apoc.date.format(timestamp(), 'yyyy-MM-dd') as date")
            date_str = result.single()["date"]
            assert len(date_str) == 10  # YYYY-MM-DD format
            
            # Should be able to use APOC text functions
            result = session.run("RETURN apoc.text.capitalize('hello world') as text")
            text = result.single()["text"]
            assert text == "Hello World"
    
    def test_bc_app_can_access_fulltext_index(self, neo4j_driver, setup_test_data):
        """Test that bc_app user can use the BC_VerseText fulltext index."""
        with neo4j_driver.session(database="neo4j") as session:
            # Should be able to use fulltext search on BC_Verse nodes
            try:
                result = session.run("""
                    CALL db.index.fulltext.queryNodes('BC_VerseText', 'God love*')
                    YIELD node, score
                    RETURN node.text as text, score
                    LIMIT 5
                """)
                # Should not raise an exception
                records = list(result)
                assert isinstance(records, list)
            except ClientError as e:
                # If index doesn't exist yet, that's okay for this test
                if "No such index" not in str(e):
                    raise
    
    @pytest.mark.asyncio
    async def test_rbac_with_application_queries(self, neo4j_driver, setup_test_data):
        """Test RBAC with actual application-style queries."""
        with neo4j_driver.session(database="neo4j") as session:
            # Test daily bread style query (should work)
            result = session.run("""
                MATCH (u:BC_User {id: 'test_user_rbac'})-[r:READ]->(v:BC_Verse)
                WHERE r.timestamp >= datetime() - duration({days: 90})
                RETURN v.id as verse_id, v.text as text
                ORDER BY r.timestamp DESC
                LIMIT 20
            """)
            records = list(result)
            assert isinstance(records, list)  # Should not raise exception
            
            # Test chat service style query (should work)
            result = session.run("""
                MATCH (u:BC_User {id: 'test_user_rbac'})-[r:READ]->(v:BC_Verse)
                RETURN v.book as book, v.chapter as chapter, v.verse as verse,
                       v.text as text, r.timestamp as read_at
                ORDER BY r.timestamp DESC
                LIMIT 20
            """)
            records = list(result)
            assert isinstance(records, list)  # Should not raise exception


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
