"""
Tests for speech API endpoints.
"""

import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient
from fastapi import WebSocket

from backend.main import app
from backend.core.auth import User


@pytest.fixture
def client():
    """Test client fixture."""
    return TestClient(app)


@pytest.fixture
def mock_premium_user():
    """Mock premium user fixture."""
    user = MagicMock(spec=User)
    user.uid = "test-premium-user"
    user.email = "<EMAIL>"
    user.custom_claims = {"premium": True}
    return user


@pytest.fixture
def mock_regular_user():
    """Mock regular user fixture."""
    user = MagicMock(spec=User)
    user.uid = "test-regular-user"
    user.email = "<EMAIL>"
    user.custom_claims = {}
    return user


class TestSpeechEndpoints:
    """Test speech API endpoints."""

    @patch('backend.routers.speech.require_premium')
    @patch('aiohttp.ClientSession.post')
    def test_text_to_speech_success(self, mock_post, mock_require_premium, client, mock_premium_user):
        """Test successful text-to-speech conversion."""
        mock_require_premium.return_value = mock_premium_user
        
        # Mock XTTS response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.content.iter_chunked.return_value = [b'audio_chunk_1', b'audio_chunk_2']
        mock_post.return_value.__aenter__.return_value = mock_response

        response = client.post(
            "/v1/speech/tts",
            json={
                "text": "Hello, this is a test message.",
                "voice": "english_female",
                "language": "en"
            },
            headers={"Authorization": "Bearer test-token"}
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == "audio/opus"

    @patch('backend.routers.speech.require_premium')
    def test_text_to_speech_premium_required(self, mock_require_premium, client):
        """Test TTS with non-premium user."""
        from fastapi import HTTPException
        mock_require_premium.side_effect = HTTPException(status_code=402, detail="Premium required")

        response = client.post(
            "/v1/speech/tts",
            json={"text": "Hello world"},
            headers={"Authorization": "Bearer test-token"}
        )

        assert response.status_code == 402

    @patch('backend.routers.speech.require_premium')
    def test_text_to_speech_empty_text(self, mock_require_premium, client, mock_premium_user):
        """Test TTS with empty text."""
        mock_require_premium.return_value = mock_premium_user

        response = client.post(
            "/v1/speech/tts",
            json={"text": ""},
            headers={"Authorization": "Bearer test-token"}
        )

        assert response.status_code == 400
        assert "empty" in response.json()["detail"].lower()

    @patch('backend.routers.speech.require_premium')
    def test_text_to_speech_text_too_long(self, mock_require_premium, client, mock_premium_user):
        """Test TTS with text that's too long."""
        mock_require_premium.return_value = mock_premium_user

        long_text = "a" * 1001  # Exceeds 1000 character limit
        response = client.post(
            "/v1/speech/tts",
            json={"text": long_text},
            headers={"Authorization": "Bearer test-token"}
        )

        assert response.status_code == 400
        assert "too long" in response.json()["detail"].lower()

    @patch('backend.routers.speech.require_premium')
    @patch('aiohttp.ClientSession.post')
    def test_text_to_speech_service_error(self, mock_post, mock_require_premium, client, mock_premium_user):
        """Test TTS when XTTS service returns error."""
        mock_require_premium.return_value = mock_premium_user
        
        # Mock XTTS error response
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text.return_value = "Internal server error"
        mock_post.return_value.__aenter__.return_value = mock_response

        response = client.post(
            "/v1/speech/tts",
            json={"text": "Hello world"},
            headers={"Authorization": "Bearer test-token"}
        )

        assert response.status_code == 503
        assert "unavailable" in response.json()["detail"].lower()

    @patch('aiohttp.ClientSession.get')
    def test_speech_health_all_healthy(self, mock_get, client):
        """Test speech health endpoint when all services are healthy."""
        # Mock healthy responses
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_get.return_value.__aenter__.return_value = mock_response

        response = client.get("/v1/speech/health")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["services"]["whisper"]["status"] == "healthy"
        assert data["services"]["xtts"]["status"] == "healthy"

    @patch('aiohttp.ClientSession.get')
    def test_speech_health_service_down(self, mock_get, client):
        """Test speech health endpoint when one service is down."""
        # Mock mixed responses
        def side_effect(*args, **kwargs):
            mock_response = AsyncMock()
            if "whisper" in str(args[0]):
                mock_response.status = 200  # Whisper healthy
            else:
                mock_response.status = 500  # XTTS unhealthy
            return mock_response

        mock_get.return_value.__aenter__.side_effect = side_effect

        response = client.get("/v1/speech/health")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "degraded"
        assert data["services"]["whisper"]["status"] == "healthy"
        assert data["services"]["xtts"]["status"] == "unhealthy"


class TestSTTWebSocket:
    """Test speech-to-text WebSocket endpoint."""

    @pytest.mark.asyncio
    @patch('backend.routers.speech.require_premium')
    async def test_stt_websocket_connection(self, mock_require_premium, mock_premium_user):
        """Test STT WebSocket connection."""
        mock_require_premium.return_value = mock_premium_user

        with TestClient(app) as client:
            with client.websocket_connect("/v1/speech/stt") as websocket:
                # Connection should be established
                assert websocket is not None

    @pytest.mark.asyncio
    @patch('backend.routers.speech.require_premium')
    @patch('aiohttp.ClientSession.post')
    async def test_stt_websocket_transcription(self, mock_post, mock_require_premium, mock_premium_user):
        """Test STT WebSocket transcription."""
        mock_require_premium.return_value = mock_premium_user
        
        # Mock Whisper response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            "text": "Hello world",
            "confidence": 0.95
        }
        mock_post.return_value.__aenter__.return_value = mock_response

        with TestClient(app) as client:
            with client.websocket_connect("/v1/speech/stt") as websocket:
                # Send audio data
                audio_data = b"fake_audio_data"
                websocket.send_bytes(audio_data)
                
                # Receive transcription
                data = websocket.receive_text()
                response = json.loads(data)
                
                assert response["type"] == "partial"
                assert response["text"] == "Hello world"
                assert response["confidence"] == 0.95

    @pytest.mark.asyncio
    @patch('backend.routers.speech.require_premium')
    @patch('aiohttp.ClientSession.post')
    async def test_stt_websocket_whisper_error(self, mock_post, mock_require_premium, mock_premium_user):
        """Test STT WebSocket when Whisper service returns error."""
        mock_require_premium.return_value = mock_premium_user
        
        # Mock Whisper error response
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_post.return_value.__aenter__.return_value = mock_response

        with TestClient(app) as client:
            with client.websocket_connect("/v1/speech/stt") as websocket:
                # Send audio data
                audio_data = b"fake_audio_data"
                websocket.send_bytes(audio_data)
                
                # Should receive error response
                data = websocket.receive_text()
                response = json.loads(data)
                
                assert response["type"] == "error"

    def test_stt_websocket_premium_required(self, client):
        """Test STT WebSocket with non-premium user."""
        with pytest.raises(Exception):  # Should fail to connect
            with client.websocket_connect("/v1/speech/stt"):
                pass


@pytest.mark.asyncio
class TestSpeechIntegration:
    """Integration tests for speech features."""

    @patch('backend.routers.speech.require_premium')
    @patch('aiohttp.ClientSession.post')
    async def test_full_voice_workflow(self, mock_post, mock_require_premium, mock_premium_user):
        """Test complete voice workflow: STT -> Chat -> TTS."""
        mock_require_premium.return_value = mock_premium_user
        
        # Mock successful responses
        mock_response = AsyncMock()
        mock_response.status = 200
        
        # For STT
        mock_response.json.return_value = {
            "text": "What is the meaning of life?",
            "confidence": 0.95
        }
        
        # For TTS
        mock_response.content.iter_chunked.return_value = [b'audio_response']
        mock_post.return_value.__aenter__.return_value = mock_response

        with TestClient(app) as client:
            # 1. STT: Convert speech to text
            with client.websocket_connect("/v1/speech/stt") as websocket:
                websocket.send_bytes(b"audio_question")
                stt_data = websocket.receive_text()
                stt_response = json.loads(stt_data)
                question_text = stt_response["text"]

            # 2. Chat: Get AI response (would normally call chat endpoint)
            ai_response = "The meaning of life is to love and serve others."

            # 3. TTS: Convert AI response to speech
            tts_response = client.post(
                "/v1/speech/tts",
                json={"text": ai_response},
                headers={"Authorization": "Bearer test-token"}
            )

            assert stt_response["text"] == "What is the meaning of life?"
            assert tts_response.status_code == 200
            assert tts_response.headers["content-type"] == "audio/opus"
