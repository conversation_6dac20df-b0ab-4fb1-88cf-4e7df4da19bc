"""
Tests for chat API endpoints.
"""

from unittest.mock import AsyncMock, patch

import pytest
from fastapi import status

from backend.services.chat_service import chat_service


class TestChatAPI:
    """Test chat API endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_chat_success(
        self,
        client,
        mock_user,
        mock_firebase_token,
        mock_chat_request,
        mock_tgi_client
    ):
        """Test successful chat creation."""

        # Mock authentication
        with patch("backend.core.auth.auth.verify_id_token") as mock_verify:
            mock_verify.return_value = {
                "uid": mock_user.uid,
                "email": mock_user.email,
                "email_verified": True
            }

            # Mock TGI client
            with patch("backend.services.tgi_client.tgi_client", mock_tgi_client):
                # Also patch the TGI client in chat service
                with patch.object(chat_service, 'tgi', mock_tgi_client):

                    # Mock database operations by patching the chat service instance
                    with patch.object(chat_service, 'mongodb') as mock_mongo:
                        mock_collection = AsyncMock()

                        # Mock get_collection to return mock collection without database check
                        def mock_get_collection(name):
                            return mock_collection
                        mock_mongo.get_collection = mock_get_collection

                        # Mock insert operations
                        mock_collection.insert_one = AsyncMock()
                        mock_collection.insert_many = AsyncMock()
                        mock_collection.update_one = AsyncMock()
                        mock_collection.find_one = AsyncMock(return_value=None)  # No existing thread
                        mock_collection.replace_one = AsyncMock()

                        # Mock Neo4j
                        with patch.object(chat_service, 'neo4j') as mock_neo4j:
                            mock_neo4j.execute_query = AsyncMock(return_value=[])

                            response = await client.post(
                                "/v1/chat",
                                json=mock_chat_request.model_dump(),
                                headers={"Authorization": f"Bearer {mock_firebase_token}"}
                            )

                            assert response.status_code == status.HTTP_200_OK
                            data = response.json()

                            assert "thread_id" in data
                            assert "answer" in data
                            assert "cursor" in data
                            assert data["answer"] == "This is a test response from the AI assistant."
    
    @pytest.mark.asyncio
    async def test_create_chat_unauthorized(self, client, mock_chat_request):
        """Test chat creation without authentication."""
        
        response = await client.post(
            "/v1/chat",
            json=mock_chat_request.model_dump()
        )
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    @pytest.mark.asyncio
    async def test_create_chat_invalid_token(self, client, mock_chat_request):
        """Test chat creation with invalid token."""
        
        with patch("backend.core.auth.auth.verify_id_token") as mock_verify:
            mock_verify.side_effect = Exception("Invalid token")
            
            response = await client.post(
                "/v1/chat",
                json=mock_chat_request.model_dump(),
                headers={"Authorization": "Bearer invalid-token"}
            )
            
            assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    @pytest.mark.asyncio
    async def test_create_chat_tgi_error(
        self,
        client,
        mock_user,
        mock_firebase_token,
        mock_chat_request
    ):
        """Test chat creation with TGI service error."""
        
        with patch("backend.core.auth.auth.verify_id_token") as mock_verify:
            mock_verify.return_value = {
                "uid": mock_user.uid,
                "email": mock_user.email,
                "email_verified": True
            }
            
            # Mock TGI client error
            mock_tgi = AsyncMock()
            mock_tgi.generate_text.side_effect = Exception("TGI service unavailable")
            
            with patch("backend.services.tgi_client.tgi_client", mock_tgi):
                with patch("backend.core.database.mongodb_client") as mock_mongo:
                    mock_collection = AsyncMock()
                    mock_mongo.get_collection.return_value = mock_collection
                    mock_collection.find_one.return_value = None
                    
                    with patch("backend.core.database.neo4j_driver") as mock_neo4j:
                        mock_neo4j.execute_query.return_value = []
                        
                        response = await client.post(
                            "/v1/chat",
                            json=mock_chat_request.model_dump(),
                            headers={"Authorization": f"Bearer {mock_firebase_token}"}
                        )
                        
                        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    
    @pytest.mark.asyncio
    async def test_get_user_threads(
        self,
        client,
        mock_user,
        mock_firebase_token,
        mock_chat_thread
    ):
        """Test getting user's chat threads."""

        with patch("backend.core.auth.auth.verify_id_token") as mock_verify:
            mock_verify.return_value = {
                "uid": mock_user.uid,
                "email": mock_user.email,
                "email_verified": True
            }

            with patch("backend.core.database.mongodb_client") as mock_mongo:
                from unittest.mock import MagicMock

                mock_collection = MagicMock()
                mock_mongo.get_collection.return_value = mock_collection

                # Mock cursor for find operation
                mock_cursor = MagicMock()
                mock_cursor.sort.return_value = mock_cursor
                mock_cursor.skip.return_value = mock_cursor
                mock_cursor.limit.return_value = mock_cursor

                # Mock async iteration
                thread_data = mock_chat_thread.model_dump()
                thread_data["_id"] = "507f1f77bcf86cd799439011"
                mock_cursor.__aiter__.return_value = iter([thread_data])

                mock_collection.find.return_value = mock_cursor

                response = await client.get(
                    "/v1/chat/threads",
                    headers={"Authorization": f"Bearer {mock_firebase_token}"}
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()

                assert "threads" in data
                assert len(data["threads"]) == 1
                assert data["threads"][0]["id"] == "507f1f77bcf86cd799439011"
    
    @pytest.mark.asyncio
    async def test_get_thread_messages(
        self,
        client,
        mock_user,
        mock_firebase_token,
        mock_chat_thread
    ):
        """Test getting messages from a chat thread."""

        with patch("backend.core.auth.auth.verify_id_token") as mock_verify:
            mock_verify.return_value = {
                "uid": mock_user.uid,
                "email": mock_user.email,
                "email_verified": True
            }

            # Mock chat service for thread validation
            with patch("backend.routers.chat.chat_service") as mock_chat_service:
                mock_chat_service.get_chat_thread = AsyncMock(return_value=mock_chat_thread)

                with patch("backend.core.database.mongodb_client") as mock_mongo:
                    from unittest.mock import MagicMock

                    mock_collection = MagicMock()
                    mock_mongo.get_collection.return_value = mock_collection

                    # Mock messages cursor
                    mock_cursor = MagicMock()
                    mock_cursor.sort.return_value = mock_cursor
                    mock_cursor.limit.return_value = mock_cursor

                    message_data = {
                        "_id": "507f1f77bcf86cd799439012",
                        "thread_id": "507f1f77bcf86cd799439011",
                        "role": "user",
                        "content": "Test message",
                        "timestamp": "2024-01-01T00:00:00"
                    }
                    mock_cursor.__aiter__.return_value = iter([message_data])

                    mock_collection.find.return_value = mock_cursor

                    response = await client.get(
                        "/v1/chat/threads/507f1f77bcf86cd799439011/messages",
                        headers={"Authorization": f"Bearer {mock_firebase_token}"}
                    )

                    assert response.status_code == status.HTTP_200_OK
                    data = response.json()

                    assert "messages" in data
                    assert len(data["messages"]) == 1
                    assert data["messages"][0]["id"] == "507f1f77bcf86cd799439012"
    
    @pytest.mark.asyncio
    async def test_delete_thread(
        self,
        client,
        mock_user,
        mock_firebase_token,
        mock_chat_thread
    ):
        """Test deleting a chat thread."""

        with patch("backend.core.auth.auth.verify_id_token") as mock_verify:
            mock_verify.return_value = {
                "uid": mock_user.uid,
                "email": mock_user.email,
                "email_verified": True
            }

            # Mock chat service for thread validation
            with patch("backend.routers.chat.chat_service") as mock_chat_service:
                mock_chat_service.get_chat_thread = AsyncMock(return_value=mock_chat_thread)

                with patch("backend.core.database.mongodb_client") as mock_mongo:
                    mock_collection = AsyncMock()
                    mock_mongo.get_collection.return_value = mock_collection

                    # Mock update operation
                    mock_collection.update_one = AsyncMock()

                    response = await client.delete(
                        "/v1/chat/threads/507f1f77bcf86cd799439011",
                        headers={"Authorization": f"Bearer {mock_firebase_token}"}
                    )

                    assert response.status_code == status.HTTP_200_OK
                    data = response.json()

                    assert data["message"] == "Thread deleted successfully"
                    assert data["thread_id"] == "507f1f77bcf86cd799439011"
