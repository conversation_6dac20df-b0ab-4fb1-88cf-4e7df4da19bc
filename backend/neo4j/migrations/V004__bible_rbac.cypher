// V004__bible_rbac.cypher
// Create isolated role and user for Bible Companion backend
// Implements RBAC to prevent access to AiLex_* or other tenant data

// ============================================================================
// CREATE BIBLE COMPANION ROLE
// ============================================================================

// Create dedicated role for Bible Companion application
CREATE ROLE bible_app IF NOT EXISTS;

// ============================================================================
// GRANT PERMISSIONS TO BIBLE COMPANION ROLE
// ============================================================================

// Grant full access to BC_* labeled nodes and relationships
GRANT MATCH, WRITE ON GRAPH ELEMENTS BC_* TO bible_app;

// Grant access to system functions needed for application
GRANT EXECUTE ON PROCEDURE apoc.* TO bible_app;
GRANT EXECUTE ON FUNCTION apoc.* TO bible_app;

// Grant access to built-in procedures and functions
GRANT EXECUTE ON PROCEDURE db.* TO bible_app;
GRANT EXECUTE ON FUNCTION db.* TO bible_app;

// Grant access to schema operations for migrations
GRANT CREATE ON GRAPH * TO bible_app;
GRANT DROP ON GRAPH * TO bible_app;

// ============================================================================
// DENY ACCESS TO OTHER TENANT DATA
// ============================================================================

// Explicitly deny access to AiLex tenant data
DENY MATCH ON GRAPH ELEMENTS AiLex_* TO bible_app;
DENY WRITE ON GRAPH ELEMENTS AiLex_* TO bible_app;

// Deny access to any other potential tenant prefixes
DENY MATCH ON GRAPH ELEMENTS Other_* TO bible_app;
DENY WRITE ON GRAPH ELEMENTS Other_* TO bible_app;

// ============================================================================
// CREATE BIBLE COMPANION APPLICATION USER
// ============================================================================

// Create user for Bible Companion backend application
// Password will be provided via BC_APP_PW environment variable
CREATE USER bc_app IF NOT EXISTS
SET PASSWORD $BC_APP_PW
SET PASSWORD CHANGE NOT REQUIRED;

// ============================================================================
// ASSIGN ROLE TO USER
// ============================================================================

// Grant the bible_app role to bc_app user
GRANT ROLE bible_app TO bc_app;

// ============================================================================
// VERIFICATION QUERIES
// ============================================================================

// Show created role and its permissions
SHOW ROLES YIELD role, member
WHERE role = 'bible_app'
RETURN role, member;

// Show user and assigned roles
SHOW USERS YIELD user, roles, suspended
WHERE user = 'bc_app'
RETURN user, roles, suspended;

// Test access to BC_ nodes (should work)
MATCH (u:BC_User) 
RETURN count(u) as bc_user_count
LIMIT 1;

// ============================================================================
// SECURITY NOTES
// ============================================================================

// This migration creates:
// 1. A dedicated 'bible_app' role with access only to BC_* labeled data
// 2. A 'bc_app' user assigned to this role
// 3. Explicit denial of access to AiLex_* and other tenant data
// 4. Necessary permissions for APOC and system functions
//
// After running this migration:
// 1. Update your application's Neo4j connection to use bc_app user
// 2. Set NEO4J_USER=bc_app in your environment variables
// 3. Set NEO4J_PASSWORD to the same value as BC_APP_PW
// 4. Test that the application can only access BC_* labeled nodes
//
// Security Benefits:
// - Complete isolation from other tenant data (AiLex_*, etc.)
// - Principle of least privilege - only access to required data
// - Audit trail of which application is accessing the database
// - Preparation for future migration to dedicated instance
