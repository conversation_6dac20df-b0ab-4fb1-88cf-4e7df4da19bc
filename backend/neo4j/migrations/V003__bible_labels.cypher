// V003__bible_labels.cypher
// Relabel existing nodes with BC_ prefix for Bible Companion isolation
// Uses APOC for safe batch processing to avoid memory issues

// ============================================================================
// RELABEL EXISTING NODES IN SAFE BATCHES
// ============================================================================

// Relabel User nodes to BC_User
CALL apoc.periodic.iterate(
  "MATCH (n:User) RETURN n",
  "SET n:BC_User REMOVE n:User",
  {batchSize:10000}
) YIELD batches, total, timeTaken, committedOperations, failedOperations, failedBatches, retries, errorMessages, batch, operations
RETURN 'User->BC_User' as operation, batches, total, timeTaken, committedOperations, failedOperations;

// Relabel Verse nodes to BC_Verse
CALL apoc.periodic.iterate(
  "MATCH (n:Verse) RETURN n",
  "SET n:BC_Verse REMOVE n:Verse",
  {batchSize:10000}
) YIELD batches, total, timeTaken, committedOperations, failedOperations, failedBatches, retries, errorMessages, batch, operations
RETURN 'Verse->BC_Verse' as operation, batches, total, timeTaken, committedOperations, failedOperations;

// Relabel Circle nodes to BC_Circle
CALL apoc.periodic.iterate(
  "MATCH (n:Circle) RETURN n",
  "SET n:BC_Circle REMOVE n:Circle",
  {batchSize:10000}
) YIELD batches, total, timeTaken, committedOperations, failedOperations, failedBatches, retries, errorMessages, batch, operations
RETURN 'Circle->BC_Circle' as operation, batches, total, timeTaken, committedOperations, failedOperations;

// ============================================================================
// RECREATE UNIQUE CONSTRAINTS WITH NEW LABELS
// ============================================================================

// Drop old constraints (if they exist)
DROP CONSTRAINT user_id_unique IF EXISTS;
DROP CONSTRAINT verse_reference_unique IF EXISTS;
DROP CONSTRAINT circle_id_unique IF EXISTS;

// Create new constraints with BC_ prefix
CREATE CONSTRAINT bc_user_pk IF NOT EXISTS
FOR (u:BC_User) REQUIRE u.id IS UNIQUE;

CREATE CONSTRAINT bc_verse_pk IF NOT EXISTS
FOR (v:BC_Verse) REQUIRE (v.lang, v.ver, v.book, v.chapter, v.verse) IS UNIQUE;

CREATE CONSTRAINT bc_circle_pk IF NOT EXISTS
FOR (c:BC_Circle) REQUIRE c.id IS UNIQUE;

// ============================================================================
// RECREATE INDEXES WITH NEW LABELS
// ============================================================================

// Drop old indexes (if they exist)
DROP INDEX user_email_index IF EXISTS;
DROP INDEX verse_book_index IF EXISTS;
DROP INDEX verse_lang_ver_index IF EXISTS;
DROP INDEX circle_name_index IF EXISTS;
DROP INDEX circle_visibility_index IF EXISTS;
DROP INDEX circle_owner_index IF EXISTS;

// Create new indexes with BC_ prefix
CREATE INDEX bc_user_email_index IF NOT EXISTS
FOR (u:BC_User) ON (u.email);

CREATE INDEX bc_verse_book_index IF NOT EXISTS
FOR (v:BC_Verse) ON (v.book);

CREATE INDEX bc_verse_lang_ver_index IF NOT EXISTS
FOR (v:BC_Verse) ON (v.lang, v.ver);

CREATE INDEX bc_circle_name_index IF NOT EXISTS
FOR (c:BC_Circle) ON (c.name);

CREATE INDEX bc_circle_visibility_index IF NOT EXISTS
FOR (c:BC_Circle) ON (c.visibility);

CREATE INDEX bc_circle_owner_index IF NOT EXISTS
FOR (c:BC_Circle) ON (c.ownerId);

// ============================================================================
// RECREATE FULL-TEXT SEARCH INDEX
// ============================================================================

// Drop old full-text index
CALL db.index.fulltext.drop("VerseText") YIELD name
RETURN "Dropped old VerseText index: " + name as result;

// Create new full-text search index for BC_Verse text
CALL db.index.fulltext.createNodeIndex(
  'BC_VerseText', 
  ['BC_Verse'], 
  ['text']
) YIELD name, entityType, labelsOrTypes, properties, provider
RETURN 'Created BC_VerseText index: ' + name as result, entityType, labelsOrTypes, properties, provider;

// ============================================================================
// UPDATE APOC TRIGGERS FOR NEW LABELS
// ============================================================================

// Remove old triggers
CALL apoc.trigger.remove('circleMemberCountOnCreate') YIELD name
RETURN 'Removed trigger: ' + name as result;

CALL apoc.trigger.remove('circleMemberCountOnDelete') YIELD name
RETURN 'Removed trigger: ' + name as result;

// Create new triggers with BC_ labels
CALL apoc.trigger.add(
  'bc_circleMemberCountOnCreate',
  "UNWIND $createdRelationships AS r
   WITH r WHERE type(r) = 'MEMBER_OF'
   MATCH (c:BC_Circle)<-[:MEMBER_OF]-(u:BC_User)
   WHERE id(c) = endNode(r).id
   WITH c, count(u) AS cnt
   SET c.memberCount = cnt,
       c.updatedAt = datetime()",
  {phase: 'after'}
) YIELD name, query, selector, params, installed, paused
RETURN 'Created trigger: ' + name as result, installed, paused;

CALL apoc.trigger.add(
  'bc_circleMemberCountOnDelete',
  "UNWIND $deletedRelationships AS r
   WITH r WHERE type(r) = 'MEMBER_OF'
   MATCH (c:BC_Circle)
   WHERE id(c) = endNode(r).id
   OPTIONAL MATCH (c)<-[:MEMBER_OF]-(u:BC_User)
   WITH c, count(u) AS cnt
   SET c.memberCount = cnt,
       c.updatedAt = datetime()",
  {phase: 'after'}
) YIELD name, query, selector, params, installed, paused
RETURN 'Created trigger: ' + name as result, installed, paused;

// ============================================================================
// VERIFICATION
// ============================================================================

// Count nodes with new labels
MATCH (u:BC_User) 
WITH count(u) as userCount
MATCH (v:BC_Verse)
WITH userCount, count(v) as verseCount
MATCH (c:BC_Circle)
WITH userCount, verseCount, count(c) as circleCount
RETURN userCount, verseCount, circleCount;
