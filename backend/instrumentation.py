"""
Instrumentation and metrics for Bible Companion Backend.

This module provides Prometheus metrics for monitoring GPU cold starts
and other application metrics for Fly.io autoscaling.
"""

from prometheus_client import Counter, Histogram, Gauge, start_http_server
import structlog

logger = structlog.get_logger()

# GPU Cold Start Metrics
GPU_COLD_START = Counter(
    "gpu_cold_start_total",
    "Total number of GPU machine cold starts on Fly.io"
)

# Chat Request Metrics
CHAT_REQUESTS = Counter(
    "chat_requests_total",
    "Total number of chat requests",
    ["status", "model"]
)

CHAT_REQUEST_DURATION = Histogram(
    "chat_request_duration_seconds",
    "Duration of chat requests in seconds",
    ["model"]
)

# TGI Connection Metrics
TGI_CONNECTIONS = Gauge(
    "tgi_active_connections",
    "Number of active connections to TGI service"
)

TGI_RESPONSE_TIME = Histogram(
    "tgi_response_time_seconds",
    "TGI response time in seconds"
)

def record_gpu_cold_start():
    """Record a GPU cold start event."""
    GPU_COLD_START.inc()
    logger.info("GPU cold start recorded")

def record_chat_request(status: str, model: str = "gemma-2b"):
    """Record a chat request."""
    CHAT_REQUESTS.labels(status=status, model=model).inc()

def record_chat_duration(duration: float, model: str = "gemma-2b"):
    """Record chat request duration."""
    CHAT_REQUEST_DURATION.labels(model=model).observe(duration)

def record_tgi_response_time(duration: float):
    """Record TGI response time."""
    TGI_RESPONSE_TIME.observe(duration)

def set_tgi_connections(count: int):
    """Set the number of active TGI connections."""
    TGI_CONNECTIONS.set(count)

def start_metrics_server(port: int = 9091):
    """Start Prometheus metrics server."""
    try:
        start_http_server(port)
        logger.info(f"Metrics server started on port {port}")
    except Exception as e:
        logger.error(f"Failed to start metrics server: {e}")
