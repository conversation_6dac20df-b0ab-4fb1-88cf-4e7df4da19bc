"""
Setup script for Bible Companion Backend.
"""

from setuptools import find_packages, setup

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="bible-companion-backend",
    version="1.0.0",
    author="Bible Companion Team",
    author_email="<EMAIL>",
    description="Backend API for Bible Companion app with Gemma chat integration",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/Jpkay/bible",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Framework :: FastAPI",
    ],
    python_requires=">=3.9",
    install_requires=[
        "fastapi>=0.104.1",
        "uvicorn[standard]>=0.24.0",
        "pydantic>=2.5.0",
        "pydantic-settings>=2.1.0",
        "pymongo>=4.6.0",
        "motor>=3.3.2",
        "neo4j>=5.15.0",
        "python-jose[cryptography]>=3.3.0",
        "python-multipart>=0.0.6",
        "firebase-admin>=6.4.0",
        "httpx>=0.25.2",
        "aiohttp>=3.9.1",
        "langchain>=0.1.0",
        "langchain-community>=0.0.10",
        "slowapi>=0.1.9",
        "python-dotenv>=1.0.0",
        "structlog>=23.2.0",
        "prometheus-client>=0.19.0",
        "marshmallow>=3.20.1",
        "tenacity>=8.2.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-mock>=3.12.0",
            "black>=23.11.0",
            "isort>=5.12.0",
            "flake8>=6.1.0",
            "mypy>=1.7.1",
        ],
        "test": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-mock>=3.12.0",
            "httpx>=0.25.2",
        ],
    },
    entry_points={
        "console_scripts": [
            "bible-companion-api=backend.main:main",
        ],
    },
)
