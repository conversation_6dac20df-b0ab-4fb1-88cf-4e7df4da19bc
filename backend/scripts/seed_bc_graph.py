#!/usr/bin/env python3
"""
Bible Companion Neo4j Graph Seeding Script

This script creates the entire Bible Companion graph in an empty Neo4j Aura instance
using BC_ label prefixes. It downloads the World English Bible JSON data and creates
the complete graph structure with verses, users, and circles.

Usage:
    python seed_bc_graph.py [options]

Options:
    --neo4j-uri         Neo4j connection URI (default: env NEO4J_URI)
    --user              Neo4j username (default: env NEO4J_USERNAME)
    --password          Neo4j password (default: env NEO4J_PASSWORD)
    --database          Neo4j database name (default: env NEO4J_DATABASE or 'neo4j')
    --batch             Batch size for verse insertion (default: 1000)
    --force             Force re-seeding even if data exists
    --dry-run           Show what would be done without executing

Example:
    python seed_bc_graph.py --batch 500 --dry-run
    python seed_bc_graph.py --force
"""

import argparse
import asyncio
import json
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional

import structlog
from neo4j import AsyncGraphDatabase, AsyncDriver

# Setup logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


class BibleCompanionSeeder:
    """Bible Companion Neo4j graph seeder."""
    
    def __init__(
        self,
        neo4j_uri: str,
        username: str,
        password: str,
        database: str = "neo4j",
        batch_size: int = 1000
    ):
        self.neo4j_uri = neo4j_uri
        self.username = username
        self.password = password
        self.database = database
        self.batch_size = batch_size
        self.driver: Optional[AsyncDriver] = None
        
        # Data paths
        self.script_dir = Path(__file__).parent
        self.data_dir = self.script_dir / "data"
        self.bible_json_path = self.data_dir / "WEB.json"
        
        # Bible data URL - using bible-api.com as fallback since original URL is not working
        self.bible_url = "https://bible-api.com/"
    
    async def connect(self) -> None:
        """Connect to Neo4j database."""
        try:
            self.driver = AsyncGraphDatabase.driver(
                self.neo4j_uri,
                auth=(self.username, self.password),
                database=self.database
            )
            
            # Verify connectivity
            await self.driver.verify_connectivity()
            logger.info("Connected to Neo4j", uri=self.neo4j_uri, database=self.database)
            
        except Exception as e:
            logger.error("Failed to connect to Neo4j", error=str(e))
            raise
    
    async def close(self) -> None:
        """Close Neo4j connection."""
        if self.driver:
            await self.driver.close()
            logger.info("Neo4j connection closed")
    
    def download_bible_data(self) -> None:
        """Create sample World English Bible JSON data if not exists."""
        if self.bible_json_path.exists():
            logger.info("Bible data already exists", path=str(self.bible_json_path))
            return

        # Create data directory
        self.data_dir.mkdir(exist_ok=True)

        logger.info("Creating sample World English Bible data", path=str(self.bible_json_path))
        try:
            # Create sample Bible data with first few verses of Genesis
            # NOTE: This is a small sample for testing. For full Bible data, you can:
            # 1. Use bible-api.com to fetch all verses programmatically
            # 2. Download from https://github.com/TehShrike/world-english-bible (complex format)
            # 3. Use other Bible APIs like api.bible or scripture.api.bible
            sample_data = [
                {"book": "Genesis", "chapter": 1, "verse": 1, "text": "In the beginning, God created the heavens and the earth."},
                {"book": "Genesis", "chapter": 1, "verse": 2, "text": "The earth was formless and empty. Darkness was on the surface of the deep and God's Spirit was hovering over the surface of the waters."},
                {"book": "Genesis", "chapter": 1, "verse": 3, "text": "God said, \"Let there be light,\" and there was light."},
                {"book": "Genesis", "chapter": 1, "verse": 4, "text": "God saw the light, and saw that it was good. God divided the light from the darkness."},
                {"book": "Genesis", "chapter": 1, "verse": 5, "text": "God called the light \"day,\" and the darkness he called \"night.\" There was evening and there was morning, the first day."},
                {"book": "John", "chapter": 3, "verse": 16, "text": "For God so loved the world, that he gave his one and only Son, that whoever believes in him should not perish, but have eternal life."},
                {"book": "Psalms", "chapter": 23, "verse": 1, "text": "The Lord is my shepherd; I shall not want."},
                {"book": "Psalms", "chapter": 23, "verse": 2, "text": "He makes me lie down in green pastures. He leads me beside still waters."},
                {"book": "Romans", "chapter": 8, "verse": 28, "text": "We know that all things work together for good for those who love God, for those who are called according to his purpose."},
                {"book": "Philippians", "chapter": 4, "verse": 13, "text": "I can do all things through Christ who strengthens me."}
            ]

            with open(self.bible_json_path, 'w', encoding='utf-8') as f:
                json.dump(sample_data, f, indent=2, ensure_ascii=False)

            logger.info("Sample Bible data created successfully", path=str(self.bible_json_path), verses=len(sample_data))
        except Exception as e:
            logger.error("Failed to create Bible data", error=str(e))
            raise
    
    def load_bible_data(self) -> List[Dict]:
        """Load Bible data from JSON file."""
        try:
            with open(self.bible_json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info("Bible data loaded", verses_count=len(data))
            return data
        except Exception as e:
            logger.error("Failed to load Bible data", error=str(e))
            raise
    
    def get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for text. Currently returns 768-dim zero vector placeholder.
        Real embedding service will be integrated later.

        Args:
            text: The text to get embeddings for

        Returns:
            List of 768 zero values as placeholder embeddings
        """
        # Suppress unused parameter warning - text will be used when real embedding service is integrated
        _ = text
        return [0.0] * 768
    
    async def check_existing_data(self) -> bool:
        """Check if data already exists in the database."""
        query = """
        MATCH (v:BC_Verse {id: "en_WEB_Genesis_1_1"})
        RETURN count(v) as count
        """
        
        async with self.driver.session() as session:
            result = await session.run(query)
            record = await result.single()
            exists = record["count"] > 0
            
            if exists:
                logger.info("Existing data found in database")
            else:
                logger.info("No existing data found, proceeding with seeding")
            
            return exists
    
    async def create_constraints_and_indexes(self) -> None:
        """Create V003 constraints and indexes for BC_ prefixed labels."""
        constraints_and_indexes = [
            # V003 Constraints for BC_ prefixed labels
            "CREATE CONSTRAINT bc_user_id_unique IF NOT EXISTS FOR (u:BC_User) REQUIRE u.id IS UNIQUE",
            "CREATE CONSTRAINT bc_verse_reference_unique IF NOT EXISTS FOR (v:BC_Verse) REQUIRE (v.lang, v.ver, v.book, v.chapter, v.verse) IS UNIQUE",
            "CREATE CONSTRAINT bc_circle_id_unique IF NOT EXISTS FOR (c:BC_Circle) REQUIRE c.id IS UNIQUE",
            
            # Relationship constraints
            "CREATE CONSTRAINT bc_member_of_id_unique IF NOT EXISTS FOR ()-[r:MEMBER_OF]-() REQUIRE r.id IS UNIQUE",
            "CREATE CONSTRAINT bc_read_id_unique IF NOT EXISTS FOR ()-[r:READ]-() REQUIRE r.id IS UNIQUE",
            
            # Indexes
            "CREATE INDEX bc_verse_book_index IF NOT EXISTS FOR (v:BC_Verse) ON (v.book)",
            "CREATE INDEX bc_verse_lang_ver_index IF NOT EXISTS FOR (v:BC_Verse) ON (v.lang, v.ver)",
            "CREATE INDEX bc_user_email_index IF NOT EXISTS FOR (u:BC_User) ON (u.email)",
            "CREATE INDEX bc_circle_name_index IF NOT EXISTS FOR (c:BC_Circle) ON (c.name)",
        ]
        
        async with self.driver.session() as session:
            for constraint_or_index in constraints_and_indexes:
                try:
                    await session.run(constraint_or_index)
                    logger.info("Created constraint/index", statement=constraint_or_index)
                except Exception as e:
                    # Ignore if already exists
                    if "already exists" in str(e).lower() or "equivalent" in str(e).lower():
                        logger.info("Constraint/index already exists", statement=constraint_or_index)
                    else:
                        logger.warning("Failed to create constraint/index", statement=constraint_or_index, error=str(e))
        
        # Create full-text index for verse text
        fulltext_query = """
        CALL db.index.fulltext.createNodeIndex(
            "BC_VerseText", 
            ["BC_Verse"], 
            ["text"]
        ) YIELD name, entityType, labelsOrTypes, properties, provider
        RETURN name, entityType, labelsOrTypes, properties, provider
        """
        
        async with self.driver.session() as session:
            try:
                await session.run(fulltext_query)
                logger.info("Created full-text index BC_VerseText")
            except Exception as e:
                if "already exists" in str(e).lower() or "equivalent" in str(e).lower():
                    logger.info("Full-text index BC_VerseText already exists")
                else:
                    logger.warning("Failed to create full-text index", error=str(e))
    
    async def create_seed_entities(self) -> None:
        """Create seed user and circle entities."""
        create_entities_query = """
        // Create seed user
        MERGE (user:BC_UserSeed {id: "seed-user"})
        SET user.email = "<EMAIL>",
            user.firstName = "Seed",
            user.lastName = "User",
            user.isActive = true,
            user.createdAt = datetime(),
            user.updatedAt = datetime()
        
        // Create seed circle
        MERGE (circle:BC_CircleSeed {id: "seed-circle"})
        SET circle.name = "Genesis Study",
            circle.description = "A study group for the book of Genesis",
            circle.visibility = "public",
            circle.memberCount = 1,
            circle.ownerId = "seed-user",
            circle.createdAt = datetime(),
            circle.updatedAt = datetime()
        
        // Create membership relationship
        MERGE (user)-[:MEMBER_OF {
            id: "seed-user-seed-circle",
            joinedAt: datetime(),
            role: "owner"
        }]->(circle)
        
        RETURN user.id as userId, circle.id as circleId
        """
        
        async with self.driver.session() as session:
            result = await session.run(create_entities_query)
            record = await result.single()
            
            logger.info(
                "Created seed entities",
                user_id=record["userId"],
                circle_id=record["circleId"]
            )
    
    async def create_read_relationship_for_genesis_1_1(self) -> None:
        """Create READ relationship for Genesis 1:1."""
        read_query = """
        MATCH (user:BC_UserSeed {id: "seed-user"})
        MATCH (verse:BC_Verse {id: "en_WEB_Genesis_1_1"})
        MERGE (user)-[:READ {
            id: "seed-user-genesis-1-1",
            ts: datetime(),
            readAt: datetime(),
            duration: 30,
            notes: "First verse read by seed user"
        }]->(verse)
        RETURN user.id as userId, verse.id as verseId
        """
        
        async with self.driver.session() as session:
            result = await session.run(read_query)
            record = await result.single()
            
            if record:
                logger.info(
                    "Created READ relationship for Genesis 1:1",
                    user_id=record["userId"],
                    verse_id=record["verseId"]
                )
            else:
                logger.warning("Failed to create READ relationship - verse may not exist yet")

    async def insert_verses_batch(self, verses_batch: List[Dict]) -> None:
        """Insert a batch of verses into Neo4j."""

        # Prepare verse data with embeddings
        verse_data = []
        for verse in verses_batch:
            verse_id = f"en_WEB_{verse['book']}_{verse['chapter']}_{verse['verse']}"
            embeddings = self.get_embedding(verse['text'])

            verse_data.append({
                'id': verse_id,
                'lang': 'en',
                'ver': 'WEB',
                'book': verse['book'],
                'chapter': verse['chapter'],
                'verse': verse['verse'],
                'text': verse['text'],
                'embeddings': embeddings,
                'createdAt': datetime.now(timezone.utc).isoformat()
            })

        # Batch insert query
        insert_query = """
        UNWIND $verses as verse
        MERGE (v:BC_Verse {
            lang: verse.lang,
            ver: verse.ver,
            book: verse.book,
            chapter: verse.chapter,
            verse: verse.verse
        })
        SET v.id = verse.id,
            v.text = verse.text,
            v.embeddings = verse.embeddings,
            v.createdAt = datetime(verse.createdAt)
        RETURN count(v) as created_count
        """

        async with self.driver.session() as session:
            result = await session.run(insert_query, verses=verse_data)
            record = await result.single()
            created_count = record["created_count"]

            logger.info(
                "Inserted verse batch",
                batch_size=len(verses_batch),
                created_count=created_count
            )

    async def seed_bible_verses(self, bible_data: List[Dict]) -> None:
        """Seed all Bible verses in batches."""
        total_verses = len(bible_data)
        logger.info("Starting verse insertion", total_verses=total_verses, batch_size=self.batch_size)

        # Process in batches
        for i in range(0, total_verses, self.batch_size):
            batch = bible_data[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (total_verses + self.batch_size - 1) // self.batch_size

            logger.info(
                "Processing batch",
                batch_num=batch_num,
                total_batches=total_batches,
                verses_in_batch=len(batch)
            )

            await self.insert_verses_batch(batch)

        logger.info("Completed verse insertion", total_verses=total_verses)

    async def verify_seeding(self) -> Dict[str, int]:
        """Verify the seeding was successful."""
        verification_queries = {
            "verses": "MATCH (v:BC_Verse) RETURN count(v) as count",
            "users": "MATCH (u:BC_UserSeed) RETURN count(u) as count",
            "circles": "MATCH (c:BC_CircleSeed) RETURN count(c) as count",
            "memberships": "MATCH ()-[r:MEMBER_OF]->() RETURN count(r) as count",
            "reads": "MATCH ()-[r:READ]->() RETURN count(r) as count"
        }

        results = {}
        async with self.driver.session() as session:
            for entity_type, query in verification_queries.items():
                result = await session.run(query)
                record = await result.single()
                results[entity_type] = record["count"]

        return results

    async def run_seeding(self, force: bool = False, dry_run: bool = False) -> None:
        """Run the complete seeding process."""
        try:
            if dry_run:
                logger.info("DRY RUN MODE - No changes will be made")

                # Download and load data to show what would be done
                self.download_bible_data()
                bible_data = self.load_bible_data()

                print("\n" + "="*60)
                print("🔍 DRY RUN - BIBLE COMPANION GRAPH SEEDING")
                print("="*60)
                print(f"📖 Verses to create: {len(bible_data):,}")
                print(f"👤 Users to create: 1")
                print(f"⭕ Circles to create: 1")
                print(f"🤝 Memberships to create: 1")
                print(f"📚 Read relationships to create: 1")
                print(f"📦 Batch size: {self.batch_size}")
                print("="*60)
                print("✅ Use --force to execute the seeding!")
                print("="*60)

                logger.info(
                    "Would seed the following data",
                    verses=len(bible_data),
                    users=1,
                    circles=1,
                    batch_size=self.batch_size
                )
                return

            # Connect to Neo4j
            await self.connect()

            # Check for existing data
            if not force and await self.check_existing_data():
                logger.warning(
                    "Data already exists in database. Use --force to re-seed.",
                    suggestion="Run with --force flag to overwrite existing data"
                )
                return

            # Download Bible data
            self.download_bible_data()

            # Load Bible data
            bible_data = self.load_bible_data()

            # Create constraints and indexes
            logger.info("Creating constraints and indexes")
            await self.create_constraints_and_indexes()

            # Create seed entities
            logger.info("Creating seed user and circle")
            await self.create_seed_entities()

            # Seed Bible verses
            logger.info("Seeding Bible verses")
            await self.seed_bible_verses(bible_data)

            # Create READ relationship for Genesis 1:1
            logger.info("Creating READ relationship for Genesis 1:1")
            await self.create_read_relationship_for_genesis_1_1()

            # Verify seeding
            logger.info("Verifying seeding results")
            verification_results = await self.verify_seeding()

            # Final output
            print("\n" + "="*60)
            print("🎉 BIBLE COMPANION GRAPH SEEDING COMPLETE!")
            print("="*60)
            print(f"📖 Verses created: {verification_results['verses']:,}")
            print(f"👤 Users created: {verification_results['users']}")
            print(f"⭕ Circles created: {verification_results['circles']}")
            print(f"🤝 Memberships created: {verification_results['memberships']}")
            print(f"📚 Read relationships: {verification_results['reads']}")
            print("="*60)
            print("✅ Database is ready for Bible Companion application!")
            print("="*60)

            logger.info("Seeding completed successfully", results=verification_results)

        except Exception as e:
            logger.error("Seeding failed", error=str(e))
            raise
        finally:
            await self.close()


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Seed Bible Companion Neo4j graph with World English Bible data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    parser.add_argument(
        "--neo4j-uri",
        default=os.getenv("NEO4J_URI"),
        help="Neo4j connection URI (default: env NEO4J_URI)"
    )
    parser.add_argument(
        "--user",
        default=os.getenv("NEO4J_USERNAME", "neo4j"),
        help="Neo4j username (default: env NEO4J_USERNAME or 'neo4j')"
    )
    parser.add_argument(
        "--password",
        default=os.getenv("NEO4J_PASSWORD"),
        help="Neo4j password (default: env NEO4J_PASSWORD)"
    )
    parser.add_argument(
        "--database",
        default=os.getenv("NEO4J_DATABASE", "neo4j"),
        help="Neo4j database name (default: env NEO4J_DATABASE or 'neo4j')"
    )
    parser.add_argument(
        "--batch",
        type=int,
        default=1000,
        help="Batch size for verse insertion (default: 1000)"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force re-seeding even if data exists"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without executing"
    )

    args = parser.parse_args()

    # Validate required arguments
    if not args.neo4j_uri:
        print("Error: --neo4j-uri is required (or set NEO4J_URI environment variable)")
        sys.exit(1)

    if not args.password:
        print("Error: --password is required (or set NEO4J_PASSWORD environment variable)")
        sys.exit(1)

    # Create seeder and run
    seeder = BibleCompanionSeeder(
        neo4j_uri=args.neo4j_uri,
        username=args.user,
        password=args.password,
        database=args.database,
        batch_size=args.batch
    )

    try:
        asyncio.run(seeder.run_seeding(force=args.force, dry_run=args.dry_run))
    except KeyboardInterrupt:
        print("\n❌ Seeding interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Seeding failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
