#!/usr/bin/env python3
"""
GPU Warm-up Script for Bible Companion

This script keeps the Fly.io GPU machine warm by sending periodic requests
to the chat API. This prevents cold starts during QA testing and development.

Usage:
    python warm_up.py

Environment Variables:
    API_URL: Base URL of the API (default: http://localhost:8000)
    TEST_JWT: JWT token for authentication
    WARM_UP_INTERVAL: Interval between requests in seconds (default: 1800 = 30 min)
    WARM_UP_ENABLED: Set to 'false' to disable warm-up (default: true)
"""

import os
import time
import requests
import logging
from datetime import datetime
from typing import Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GPUWarmer:
    """Keeps GPU machines warm by sending periodic requests."""
    
    def __init__(self):
        self.api_url = os.getenv("API_URL", "http://localhost:8000")
        self.test_jwt = os.getenv("TEST_JWT")
        self.interval = int(os.getenv("WARM_UP_INTERVAL", "1800"))  # 30 minutes
        self.enabled = os.getenv("WARM_UP_ENABLED", "true").lower() == "true"
        
        if not self.test_jwt:
            logger.warning("TEST_JWT not set. Warm-up requests will fail authentication.")
    
    def send_warm_up_request(self) -> bool:
        """Send a warm-up request to the chat API."""
        try:
            headers = {}
            if self.test_jwt:
                headers["Authorization"] = f"Bearer {self.test_jwt}"
            
            payload = {
                "threadId": "warm-up-thread",
                "prompt": "ping",
                "maxTokens": 10
            }
            
            response = requests.post(
                f"{self.api_url}/v1/chat",
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"Warm-up request successful: {response.status_code}")
                return True
            else:
                logger.warning(f"Warm-up request failed: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Warm-up request error: {e}")
            return False
    
    def check_health(self) -> bool:
        """Check if the API is healthy."""
        try:
            response = requests.get(
                f"{self.api_url}/healthz",
                timeout=10
            )
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False
    
    def run(self):
        """Run the warm-up loop."""
        if not self.enabled:
            logger.info("Warm-up disabled via WARM_UP_ENABLED=false")
            return
        
        logger.info(f"Starting GPU warm-up service")
        logger.info(f"API URL: {self.api_url}")
        logger.info(f"Interval: {self.interval} seconds ({self.interval/60:.1f} minutes)")
        
        while True:
            try:
                # Check if API is healthy first
                if not self.check_health():
                    logger.warning("API health check failed, skipping warm-up request")
                else:
                    # Send warm-up request
                    success = self.send_warm_up_request()
                    if success:
                        logger.info("GPU machine kept warm")
                    else:
                        logger.warning("Failed to keep GPU machine warm")
                
                # Wait for next interval
                logger.info(f"Sleeping for {self.interval} seconds...")
                time.sleep(self.interval)
                
            except KeyboardInterrupt:
                logger.info("Warm-up service stopped by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error in warm-up loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying

def main():
    """Main entry point."""
    warmer = GPUWarmer()
    warmer.run()

if __name__ == "__main__":
    main()
