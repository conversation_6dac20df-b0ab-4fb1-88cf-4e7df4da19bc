# Bible Companion Neo4j Seeding Scripts

This directory contains scripts for seeding the Bible Companion Neo4j graph database.

## seed_bc_graph.py

A comprehensive script that creates the entire Bible Companion graph in an empty Neo4j Aura instance using BC_ label prefixes.

### Features

- **Complete Graph Creation**: Creates verses, users, circles, and relationships
- **BC_ Label Prefixes**: Uses proper label prefixes for Bible Companion schema
- **Batch Processing**: Efficient batch insertion with configurable batch sizes
- **Idempotent**: Safe to run multiple times without creating duplicates
- **Sample Data**: Includes sample World English Bible verses for testing
- **V003 Constraints**: Creates all necessary constraints and indexes
- **Comprehensive Logging**: Detailed logging with structured output

### Usage

```bash
# Basic usage with environment variables
python seed_bc_graph.py

# With explicit parameters
python seed_bc_graph.py --neo4j-uri bolt://localhost:7687 --user neo4j --password mypassword

# Dry run to see what would be done
python seed_bc_graph.py --dry-run

# Force re-seeding even if data exists
python seed_bc_graph.py --force

# Custom batch size
python seed_bc_graph.py --batch 500
```

### Command Line Options

- `--neo4j-uri`: Neo4j connection URI (default: env NEO4J_URI)
- `--user`: Neo4j username (default: env NEO4J_USERNAME or 'neo4j')
- `--password`: Neo4j password (default: env NEO4J_PASSWORD)
- `--database`: Neo4j database name (default: env NEO4J_DATABASE or 'neo4j')
- `--batch`: Batch size for verse insertion (default: 1000)
- `--force`: Force re-seeding even if data exists
- `--dry-run`: Show what would be done without executing

### Environment Variables

Set these environment variables to avoid passing credentials on command line:

```bash
export NEO4J_URI="neo4j+s://your-instance.databases.neo4j.io"
export NEO4J_USERNAME="neo4j"
export NEO4J_PASSWORD="your-password"
export NEO4J_DATABASE="neo4j"  # optional, defaults to "neo4j"
```

### Graph Schema Created

#### Nodes

- **BC_Verse**: Bible verses with embeddings
  - Properties: `id`, `lang`, `ver`, `book`, `chapter`, `verse`, `text`, `embeddings`, `createdAt`
  - ID format: `"en_WEB_<book>_<chapter>_<verse>"`

- **BC_UserSeed**: Seed user for testing
  - Properties: `id`, `email`, `firstName`, `lastName`, `isActive`, `createdAt`, `updatedAt`

- **BC_CircleSeed**: Seed circle for testing
  - Properties: `id`, `name`, `description`, `visibility`, `memberCount`, `ownerId`, `createdAt`, `updatedAt`

#### Relationships

- **MEMBER_OF**: User membership in circles
  - Properties: `id`, `joinedAt`, `role`

- **READ**: User reading relationships with verses
  - Properties: `id`, `ts`, `readAt`, `duration`, `notes`

#### Constraints & Indexes

- Unique constraints on node IDs
- Composite unique constraint on verse reference
- Relationship ID constraints
- Full-text search index on verse text
- Performance indexes on common lookup fields

### Sample Data

The script creates sample data including:
- 10 sample Bible verses (Genesis 1:1-5, John 3:16, Psalms 23:1-2, Romans 8:28, Philippians 4:13)
- 1 seed user (`seed-user`)
- 1 seed circle (`seed-circle` - "Genesis Study")
- 1 membership relationship
- 1 read relationship (Genesis 1:1)

### Extending with Full Bible Data

The current script uses sample data for testing. To load the complete Bible:

1. **Use bible-api.com**: Fetch all verses programmatically
2. **Use TehShrike/world-english-bible**: Download and parse complex JSON format
3. **Use other Bible APIs**: api.bible, scripture.api.bible, etc.
4. **Replace sample_data**: Update the `download_bible_data()` method

### Output

Successful execution produces:

```
============================================================
🎉 BIBLE COMPANION GRAPH SEEDING COMPLETE!
============================================================
📖 Verses created: 10
👤 Users created: 1
⭕ Circles created: 1
🤝 Memberships created: 1
📚 Read relationships: 1
============================================================
✅ Database is ready for Bible Companion application!
============================================================
```

### Error Handling

- **Connection Issues**: Clear error messages for Neo4j connectivity problems
- **Existing Data**: Warns if data already exists (use `--force` to override)
- **Validation**: Validates required parameters before execution
- **Graceful Cleanup**: Properly closes connections on errors

### Dependencies

- `neo4j`: Neo4j Python driver for async operations
- `structlog`: Structured logging
- Standard library: `asyncio`, `json`, `pathlib`, etc.

### Testing

```bash
# Test with dry run
python seed_bc_graph.py --dry-run

# Test help
python seed_bc_graph.py --help

# Test with local Neo4j (if running)
python seed_bc_graph.py --neo4j-uri bolt://localhost:7687 --password test
```

## Files Created

- `data/WEB.json`: Sample World English Bible data
- Script creates this automatically on first run

## Integration

This script is designed to work with:
- Neo4j Aura instances
- Local Neo4j installations
- Bible Companion application schema
- Daily Bread recommendation engine (requires verse embeddings)

The seeded data provides a foundation for testing the Bible Companion application's core features including verse recommendations, user interactions, and circle functionality.
