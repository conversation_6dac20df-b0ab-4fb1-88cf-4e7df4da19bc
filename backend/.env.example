# Bible Companion Backend Configuration

# Application
DEBUG=true
SECRET_KEY=your-secret-key-here
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8081", "http://localhost:19006"]

# Database
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB_NAME=bible_companion

# Neo4j
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-neo4j-password

# Neo4j RBAC - Bible Companion App User
NEO4J_USER=bc_app
BC_APP_PW=ChangeMe123!

# TGI (Text Generation Inference)
TGI_BASE_URL=http://localhost:8080
TGI_TIMEOUT=30
TGI_MAX_RETRIES=3

# Firebase
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CREDENTIALS_PATH=path/to/firebase-service-account.json

# Chat Configuration
CHAT_MAX_HISTORY=10
CHAT_CONTEXT_VERSES=20
CHAT_MAX_TOKENS=512
CHAT_TEMPERATURE=0.7

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Firebase Cloud Messaging
FCM_ENABLED=true
FCM_CREDENTIALS_PATH=path/to/firebase-service-account.json

# Daily Bread Scheduler
DAILY_BREAD_ENABLED=true
DAILY_BREAD_HOUR=5
DAILY_BREAD_MINUTE=0
DAILY_BREAD_TIMEZONE=UTC

# Logging
LOG_LEVEL=INFO

# ImageVerse Configuration
SDXL_MCP_URL=http://img-gen.mcp.example.com
REDIS_URL=redis://localhost:6379
R2_ACCESS_KEY_ID=your-r2-access-key-id
R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
R2_BUCKET_NAME=verse-media
R2_ENDPOINT_URL=https://your-account-id.r2.cloudflarestorage.com
IMAGE_GENERATION_RATE_LIMIT=5

# Premium Flux Configuration
REPLICATE_API_TOKEN=your-replicate-api-token
FLUX_MONTHLY_QUOTA=3
REALESRGAN_DOCKER_IMAGE=ghcr.io/xinntao/real-esrgan:latest
