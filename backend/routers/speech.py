"""
Speech-to-Text and Text-to-Speech endpoints for Bible Companion API.

Premium voice features using Whisper.cpp for STT and XTTS for TTS.
"""

import asyncio
import json
import logging
from typing import AsyncGenerator

import aiohttp
import structlog
from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from backend.core.auth import User, require_premium
from backend.core.config import get_settings

logger = structlog.get_logger()
router = APIRouter()
settings = get_settings()


class TTSRequest(BaseModel):
    """Text-to-speech request model."""
    text: str
    voice: str = "english_female"
    language: str = "en"


class STTResponse(BaseModel):
    """Speech-to-text response model."""
    type: str  # "partial" or "final"
    text: str
    confidence: float = 0.0


@router.websocket("/speech/stt")
async def speech_to_text_websocket(
    websocket: WebSocket,
    user: User = Depends(require_premium)
):
    """
    WebSocket endpoint for real-time speech-to-text.
    
    Premium feature that streams audio chunks to Whisper.cpp
    and returns partial/final transcriptions.
    
    **Authentication**: Requires premium Firebase JWT token
    **Rate Limit**: Premium users only
    
    Protocol:
    - Client sends binary audio chunks (Ogg format)
    - Server responds with JSON: {"type": "partial|final", "text": "...", "confidence": 0.0-1.0}
    """
    await websocket.accept()
    
    logger.info("STT WebSocket connected", user_id=user.uid)
    
    try:
        # Connect to Whisper.cpp service
        whisper_url = f"http://whisper:9000/inference"
        
        async with aiohttp.ClientSession() as session:
            while True:
                try:
                    # Receive audio chunk from client
                    audio_data = await websocket.receive_bytes()
                    
                    # Send to Whisper.cpp for transcription
                    async with session.post(
                        whisper_url,
                        data=audio_data,
                        headers={"Content-Type": "audio/ogg"}
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            
                            # Extract transcription
                            text = result.get("text", "").strip()
                            confidence = result.get("confidence", 0.0)
                            
                            if text:
                                # Send partial result
                                stt_response = STTResponse(
                                    type="partial",
                                    text=text,
                                    confidence=confidence
                                )
                                await websocket.send_text(stt_response.model_dump_json())
                        else:
                            logger.warning(
                                "Whisper.cpp error",
                                status=response.status,
                                user_id=user.uid
                            )
                            
                except WebSocketDisconnect:
                    break
                except Exception as e:
                    logger.error("STT processing error", error=str(e), user_id=user.uid)
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "text": "Speech recognition error",
                        "confidence": 0.0
                    }))
                    
    except Exception as e:
        logger.error("STT WebSocket error", error=str(e), user_id=user.uid)
    finally:
        logger.info("STT WebSocket disconnected", user_id=user.uid)


@router.post("/speech/tts")
async def text_to_speech(
    request: TTSRequest,
    user: User = Depends(require_premium)
) -> StreamingResponse:
    """
    Convert text to speech using XTTS.
    
    Premium feature that generates natural speech from text
    and streams audio/opus response.
    
    **Authentication**: Requires premium Firebase JWT token
    **Rate Limit**: Premium users only
    
    Args:
        request: TTS request with text and voice settings
        user: Authenticated premium user
        
    Returns:
        StreamingResponse: Audio stream in Opus format
        
    Raises:
        HTTPException: If TTS service is unavailable or text is invalid
    """
    if not request.text.strip():
        raise HTTPException(status_code=400, detail="Text cannot be empty")
    
    if len(request.text) > 1000:
        raise HTTPException(status_code=400, detail="Text too long (max 1000 characters)")
    
    logger.info("TTS request", user_id=user.uid, text_length=len(request.text))
    
    try:
        # Call XTTS service
        xtts_url = f"http://xtts:9100/tts"
        
        async def generate_audio() -> AsyncGenerator[bytes, None]:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "text": request.text,
                    "voice": request.voice,
                    "language": request.language,
                    "format": "opus"
                }
                
                async with session.post(xtts_url, json=payload) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(
                            "XTTS service error",
                            status=response.status,
                            error=error_text,
                            user_id=user.uid
                        )
                        raise HTTPException(
                            status_code=503,
                            detail="Text-to-speech service unavailable"
                        )
                    
                    # Stream audio chunks
                    async for chunk in response.content.iter_chunked(8192):
                        yield chunk
        
        return StreamingResponse(
            generate_audio(),
            media_type="audio/opus",
            headers={
                "Content-Disposition": "inline; filename=speech.opus",
                "Cache-Control": "no-cache"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("TTS error", error=str(e), user_id=user.uid)
        raise HTTPException(status_code=500, detail="Text-to-speech failed")


@router.get("/speech/health")
async def speech_health():
    """
    Health check for speech services.
    
    Returns:
        dict: Status of Whisper and XTTS services
    """
    services = {
        "whisper": {"status": "unknown", "url": "http://whisper:9000/health"},
        "xtts": {"status": "unknown", "url": "http://xtts:9100/health"}
    }
    
    async with aiohttp.ClientSession() as session:
        # Check Whisper.cpp
        try:
            async with session.get(services["whisper"]["url"], timeout=5) as response:
                services["whisper"]["status"] = "healthy" if response.status == 200 else "unhealthy"
        except Exception:
            services["whisper"]["status"] = "unhealthy"
        
        # Check XTTS
        try:
            async with session.get(services["xtts"]["url"], timeout=5) as response:
                services["xtts"]["status"] = "healthy" if response.status == 200 else "unhealthy"
        except Exception:
            services["xtts"]["status"] = "unhealthy"
    
    overall_status = "healthy" if all(
        service["status"] == "healthy" for service in services.values()
    ) else "degraded"
    
    return {
        "status": overall_status,
        "services": services,
        "timestamp": "2024-01-01T00:00:00Z"  # Will be replaced with actual timestamp
    }
