"""
Daily Bread recommendation engine for Bible Companion API.

This module implements the core recommendation algorithm that:
1. Analyzes user's reading history from Neo4j
2. Computes user interest vector from verse embeddings
3. Performs vector search on MongoDB Atlas
4. Scores candidates based on relevance, freshness, and personal interest
5. Returns the top recommendation
"""

import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import structlog
from sklearn.metrics.pairwise import cosine_similarity

from backend.core.config import get_settings
from backend.core.database import mongodb_client, neo4j_driver
from backend.models.recommendation import DailyRecommendation, UserInterestProfile

logger = structlog.get_logger()


class DailyBreadEngine:
    """Daily Bread recommendation engine."""
    
    def __init__(self):
        self.settings = get_settings()
        self.db = None  # Will be set when needed

    def _get_db(self):
        """Get database instance, connecting if necessary."""
        if self.db is None:
            try:
                from backend.core.database import mongodb_client
                self.db = mongodb_client.get_database()
            except Exception:
                # For testing, return None
                return None
        return self.db
    
    async def select_daily_verse(self, user_id: str) -> Optional[DailyRecommendation]:
        """
        Select daily verse recommendation for a user.
        
        Args:
            user_id: User ID to generate recommendation for
            
        Returns:
            DailyRecommendation or None if no suitable verse found
        """
        try:
            logger.info("Generating daily verse recommendation", user_id=user_id)
            
            # Check if recommendation already exists for today
            existing = await self._get_existing_recommendation(user_id)
            if existing:
                logger.info("Using existing daily recommendation", user_id=user_id)
                return existing
            
            # Get user's reading history from Neo4j
            user_history = await self._get_user_reading_history(user_id)
            if not user_history:
                logger.info("No reading history found, using fallback recommendation", user_id=user_id)
                return await self._get_fallback_recommendation(user_id)
            
            # Compute user interest vector
            interest_vector = await self._compute_interest_vector(user_history)
            if not interest_vector:
                logger.warning("Failed to compute interest vector", user_id=user_id)
                return await self._get_fallback_recommendation(user_id)
            
            # Get candidate verses using vector search
            candidates = await self._get_candidate_verses(interest_vector, user_id)
            if not candidates:
                logger.warning("No candidate verses found", user_id=user_id)
                return await self._get_fallback_recommendation(user_id)
            
            # Score and rank candidates
            scored_candidates = await self._score_candidates(
                candidates, interest_vector, user_history, user_id
            )
            
            if not scored_candidates:
                logger.warning("No scored candidates", user_id=user_id)
                return await self._get_fallback_recommendation(user_id)
            
            # Select top recommendation
            top_candidate = scored_candidates[0]
            recommendation = await self._create_recommendation(
                user_id, top_candidate, interest_vector, user_history
            )
            
            # Store recommendation in database
            await self._store_recommendation(recommendation)
            
            logger.info(
                "Daily verse recommendation generated",
                user_id=user_id,
                verse_reference=recommendation.verse_reference,
                final_score=recommendation.final_score
            )
            
            return recommendation
            
        except Exception as e:
            logger.error("Failed to generate daily verse recommendation", user_id=user_id, error=str(e))
            return await self._get_fallback_recommendation(user_id)
    
    async def _get_existing_recommendation(self, user_id: str) -> Optional[DailyRecommendation]:
        """Check if recommendation already exists for today."""
        db = self._get_db()
        if not db:
            return None

        today = datetime.utcnow().date()

        result = await db.recommendations.find_one({
            "user_id": user_id,
            "recommendation_date": {
                "$gte": datetime.combine(today, datetime.min.time()),
                "$lt": datetime.combine(today + timedelta(days=1), datetime.min.time())
            }
        })
        
        if result:
            return DailyRecommendation(**result)
        return None
    
    async def _get_user_reading_history(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's reading history from Neo4j (last 90 days)."""
        query = """
        MATCH (u:BC_User {id: $user_id})-[r:READ|SHARED|PRAYED_FOR|EMOTION]->(v:BC_Verse)
        WHERE r.timestamp >= datetime() - duration({days: 90})
        RETURN v.id as verse_id, v.book as book, v.chapter as chapter,
               v.verse as verse, v.text as text, v.embeddings as embeddings,
               r.timestamp as interaction_time, type(r) as interaction_type
        ORDER BY r.timestamp DESC
        """
        
        try:
            records = await neo4j_driver.execute_query(query, {"user_id": user_id})
            return [dict(record) for record in records]
        except Exception as e:
            logger.error("Failed to fetch user reading history", user_id=user_id, error=str(e))
            return []
    
    async def _compute_interest_vector(self, user_history: List[Dict[str, Any]]) -> Optional[List[float]]:
        """Compute user interest vector from reading history."""
        if not user_history:
            return None
        
        # Extract embeddings from user history
        embeddings = []
        for record in user_history:
            if record.get("embeddings"):
                embeddings.append(record["embeddings"])
        
        if not embeddings:
            logger.warning("No embeddings found in user history")
            return None
        
        # Compute mean embedding as interest vector
        embeddings_array = np.array(embeddings)
        interest_vector = np.mean(embeddings_array, axis=0)
        
        return interest_vector.tolist()
    
    async def _get_candidate_verses(
        self, 
        interest_vector: List[float], 
        user_id: str,
        k: int = 50
    ) -> List[Dict[str, Any]]:
        """Get candidate verses using Atlas Vector Search."""
        
        # Get verses user has already read to exclude them
        read_verse_ids = await self._get_read_verse_ids(user_id)
        
        # Vector search pipeline
        pipeline = [
            {
                "$vectorSearch": {
                    "index": "verse_embeddings_index",
                    "path": "embeddings",
                    "queryVector": interest_vector,
                    "numCandidates": k * 2,  # Get more candidates for filtering
                    "limit": k
                }
            },
            {
                "$match": {
                    "_id": {"$nin": read_verse_ids}  # Exclude already read verses
                }
            },
            {
                "$addFields": {
                    "score": {"$meta": "vectorSearchScore"}
                }
            },
            {
                "$limit": k
            }
        ]
        
        try:
            db = self._get_db()
            if not db:
                return []
            cursor = db.verses.aggregate(pipeline)
            candidates = await cursor.to_list(length=k)
            return candidates
        except Exception as e:
            logger.error("Failed to perform vector search", error=str(e))
            return []
    
    async def _get_read_verse_ids(self, user_id: str) -> List[str]:
        """Get list of verse IDs the user has already read."""
        query = """
        MATCH (u:BC_User {id: $user_id})-[:READ]->(v:BC_Verse)
        RETURN v.id as verse_id
        """
        
        try:
            records = await neo4j_driver.execute_query(query, {"user_id": user_id})
            return [record["verse_id"] for record in records]
        except Exception as e:
            logger.error("Failed to fetch read verse IDs", user_id=user_id, error=str(e))
            return []
    
    async def _score_candidates(
        self,
        candidates: List[Dict[str, Any]],
        interest_vector: List[float],
        user_history: List[Dict[str, Any]],
        user_id: str
    ) -> List[Dict[str, Any]]:
        """Score and rank candidate verses."""
        scored_candidates = []
        
        # Get user's favorite books for personal interest scoring
        favorite_books = self._get_favorite_books(user_history)
        
        for candidate in candidates:
            try:
                # Relevance score (from vector search)
                relevance_score = candidate.get("score", 0.0)
                
                # Freshness score (inverse of days since verse was added)
                freshness_score = self._calculate_freshness_score(candidate)
                
                # Personal interest score (boost for favorite books/chapters)
                personal_interest_score = self._calculate_personal_interest_score(
                    candidate, favorite_books, user_history
                )
                
                # Final weighted score
                final_score = (
                    0.5 * relevance_score +
                    0.3 * freshness_score +
                    0.2 * personal_interest_score
                )
                
                candidate.update({
                    "relevance_score": relevance_score,
                    "freshness_score": freshness_score,
                    "personal_interest_score": personal_interest_score,
                    "final_score": final_score
                })
                
                scored_candidates.append(candidate)
                
            except Exception as e:
                logger.error("Failed to score candidate", candidate_id=candidate.get("_id"), error=str(e))
                continue
        
        # Sort by final score (descending)
        scored_candidates.sort(key=lambda x: x["final_score"], reverse=True)
        
        return scored_candidates
    
    def _get_favorite_books(self, user_history: List[Dict[str, Any]]) -> List[str]:
        """Get user's favorite books based on reading frequency."""
        book_counts = {}
        for record in user_history:
            book = record.get("book")
            if book:
                book_counts[book] = book_counts.get(book, 0) + 1
        
        # Sort by frequency and return top 5
        sorted_books = sorted(book_counts.items(), key=lambda x: x[1], reverse=True)
        return [book for book, _ in sorted_books[:5]]
    
    def _calculate_freshness_score(self, candidate: Dict[str, Any]) -> float:
        """Calculate freshness score based on when verse was added."""
        # For now, assume all verses are equally fresh
        # In a real implementation, you'd check when the verse was added to the database
        return 1.0
    
    def _calculate_personal_interest_score(
        self,
        candidate: Dict[str, Any],
        favorite_books: List[str],
        user_history: List[Dict[str, Any]]
    ) -> float:
        """Calculate personal interest score."""
        score = 0.0
        
        # Boost if verse is from user's favorite book
        if candidate.get("book") in favorite_books:
            score += 0.5
        
        # Boost if verse is from same chapter user has read before
        book = candidate.get("book")
        chapter = candidate.get("chapter")
        
        for record in user_history:
            if record.get("book") == book and record.get("chapter") == chapter:
                score += 0.3
                break
        
        return min(score, 1.0)  # Cap at 1.0

    async def _create_recommendation(
        self,
        user_id: str,
        candidate: Dict[str, Any],
        interest_vector: List[float],
        user_history: List[Dict[str, Any]]
    ) -> DailyRecommendation:
        """Create recommendation object from candidate."""
        verse_reference = f"{candidate['book']} {candidate['chapter']}:{candidate['verse']}"

        # Get context verse IDs
        context_verse_ids = [record.get("verse_id") for record in user_history[:20]]
        context_verse_ids = [vid for vid in context_verse_ids if vid]  # Remove None values

        return DailyRecommendation(
            user_id=user_id,
            verse_id=str(candidate["_id"]),
            verse_text=candidate["text"],
            verse_reference=verse_reference,
            book=candidate["book"],
            chapter=candidate["chapter"],
            verse=candidate["verse"],
            translation=candidate.get("translation", "NIV"),
            relevance_score=candidate["relevance_score"],
            freshness_score=candidate["freshness_score"],
            personal_interest_score=candidate["personal_interest_score"],
            final_score=candidate["final_score"],
            context_verses=context_verse_ids,
            user_interest_vector=interest_vector
        )

    async def _store_recommendation(self, recommendation: DailyRecommendation) -> None:
        """Store recommendation in database."""
        try:
            db = self._get_db()
            if not db:
                return
            doc = recommendation.model_dump()
            doc["_id"] = doc.pop("id", None)  # Remove None id
            await db.recommendations.insert_one(doc)

        except Exception as e:
            logger.error("Failed to store recommendation", error=str(e))

    async def _get_fallback_recommendation(self, user_id: str) -> Optional[DailyRecommendation]:
        """Get fallback recommendation for users with no history."""
        try:
            db = self._get_db()
            if not db:
                return None
            # Get a popular verse (e.g., John 3:16)
            fallback_verse = await db.verses.find_one({
                "book": "John",
                "chapter": 3,
                "verse": 16
            })

            if not fallback_verse:
                # If John 3:16 doesn't exist, get any verse
                fallback_verse = await db.verses.find_one()

            if fallback_verse:
                verse_reference = f"{fallback_verse['book']} {fallback_verse['chapter']}:{fallback_verse['verse']}"

                recommendation = DailyRecommendation(
                    user_id=user_id,
                    verse_id=str(fallback_verse["_id"]),
                    verse_text=fallback_verse["text"],
                    verse_reference=verse_reference,
                    book=fallback_verse["book"],
                    chapter=fallback_verse["chapter"],
                    verse=fallback_verse["verse"],
                    translation=fallback_verse.get("translation", "NIV"),
                    relevance_score=0.5,
                    freshness_score=1.0,
                    personal_interest_score=0.5,
                    final_score=0.67,
                    context_verses=[],
                    user_interest_vector=[]
                )

                await self._store_recommendation(recommendation)
                return recommendation

            return None

        except Exception as e:
            logger.error("Failed to get fallback recommendation", user_id=user_id, error=str(e))
            return None


# Global engine instance
daily_bread_engine = DailyBreadEngine()
