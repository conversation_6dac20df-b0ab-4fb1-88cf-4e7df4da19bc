version: '3.8'

services:
  whisper:
    image: ghcr.io/ggerganov/whisper.cpp:tiny-int8
    container_name: bible-companion-whisper
    ports:
      - "9000:9000"
    command: ["--port", "9000", "--model", "tiny-int8.en"]
    environment:
      - WHISPER_MODEL=tiny-int8.en
      - WHISPER_LANGUAGE=en
    volumes:
      - whisper_models:/models
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  whisper_models:
    driver: local

networks:
  default:
    name: bible-companion-network
    external: true
