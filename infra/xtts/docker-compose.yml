version: '3.8'

services:
  xtts:
    image: ghcr.io/coqui-ai/xtts:latest
    container_name: bible-companion-xtts
    ports:
      - "9100:9100"
    environment:
      - VOICE=english_female
      - PORT=9100
      - HOST=0.0.0.0
      - MODEL_NAME=tts_models/multilingual/multi-dataset/xtts_v2
      - USE_CUDA=false
    volumes:
      - xtts_models:/app/models
      - xtts_cache:/app/cache
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9100/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  xtts_models:
    driver: local
  xtts_cache:
    driver: local

networks:
  default:
    name: bible-companion-network
    external: true
