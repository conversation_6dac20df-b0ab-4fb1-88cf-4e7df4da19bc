# TGI (Text Generation Inference) Setup

This directory contains the Docker Compose configuration for running Hugging Face's Text Generation Inference server with the Gemma 2B model.

## Prerequisites

1. **Docker & Docker Compose** installed
2. **NVIDIA GPU** with CUDA support (for optimal performance)
3. **Hugging Face Token** with access to Gemma models

## Local Development Setup

### 1. Environment Variables

Create a `.env` file in this directory:

```bash
# Required: Hugging Face token with Gemma model access
HUGGING_FACE_HUB_TOKEN=your_hf_token_here
```

### 2. Start TGI Server

```bash
# Start the TGI server
docker compose up -d

# Check logs
docker compose logs -f tgi

# Health check
curl http://localhost:8080/health
```

### 3. Test the API

```bash
# Test text generation
curl -X POST http://localhost:8080/generate \
  -H "Content-Type: application/json" \
  -d '{
    "inputs": "What is the meaning of faith in Christianity?",
    "parameters": {
      "max_new_tokens": 100,
      "temperature": 0.7,
      "top_p": 0.9,
      "do_sample": true
    }
  }'
```

## Fly.io Deployment

### 1. Install Fly CLI

```bash
# macOS
brew install flyctl

# Linux/Windows
curl -L https://fly.io/install.sh | sh
```

### 2. Create Fly App

```bash
# Login to Fly.io
fly auth login

# Create app
fly apps create bible-companion-tgi

# Set secrets
fly secrets set HUGGING_FACE_HUB_TOKEN=your_token_here
```

### 3. Deploy Configuration

Create `fly.toml`:

```toml
app = "bible-companion-tgi"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile.fly"

[env]
  MODEL_ID = "google/gemma-2b-it"
  MAX_CONCURRENT_REQUESTS = "64"
  MAX_INPUT_LENGTH = "1024"
  MAX_TOTAL_TOKENS = "2048"

[[services]]
  internal_port = 80
  protocol = "tcp"

  [[services.ports]]
    handlers = ["http"]
    port = 80

  [[services.ports]]
    handlers = ["tls", "http"]
    port = 443

[http_service]
  internal_port = 80
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true

[[vm]]
  memory = "8gb"
  cpu_kind = "performance"
  cpus = 4
```

### 4. Deploy

```bash
# Deploy to Fly.io
fly deploy

# Check status
fly status

# View logs
fly logs
```

## Configuration Options

### Model Selection

- `google/gemma-2b-it` - Smaller, faster (recommended for development)
- `google/gemma-7b-it` - Larger, more capable (production)

### Performance Tuning

- **MAX_CONCURRENT_REQUESTS**: Adjust based on available memory
- **MAX_INPUT_LENGTH**: Limit input token length
- **MAX_TOTAL_TOKENS**: Total tokens (input + output)
- **WAITING_SERVED_RATIO**: Queue management

## Monitoring

### Health Checks

```bash
# Basic health
curl http://localhost:8080/health

# Detailed metrics
curl http://localhost:8080/metrics

# Model info
curl http://localhost:8080/info
```

### Jaeger Tracing

Access Jaeger UI at http://localhost:16686 for request tracing.

## Troubleshooting

### Common Issues

1. **GPU not detected**: Ensure NVIDIA Docker runtime is installed
2. **Model download fails**: Check Hugging Face token permissions
3. **Out of memory**: Reduce batch sizes or use smaller model
4. **Slow responses**: Check GPU utilization and model size

### Logs

```bash
# TGI logs
docker compose logs tgi

# Container stats
docker stats bible-companion-tgi
```

## Security Notes

- Keep Hugging Face tokens secure
- Use HTTPS in production
- Implement rate limiting at application level
- Monitor resource usage and costs
