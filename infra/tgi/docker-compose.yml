version: '3.8'

services:
  tgi:
    image: ghcr.io/huggingface/text-generation-inference:latest
    container_name: bible-companion-tgi
    ports:
      - "8080:80"
    environment:
      - MODEL_ID=google/gemma-2b-it
      - HUGGING_FACE_HUB_TOKEN=${HUGGING_FACE_HUB_TOKEN}
      - MAX_CONCURRENT_REQUESTS=128
      - MAX_BEST_OF=2
      - MAX_STOP_SEQUENCES=4
      - MAX_INPUT_LENGTH=1024
      - MAX_TOTAL_TOKENS=2048
      - WAITING_SERVED_RATIO=1.2
      - MAX_BATCH_PREFILL_TOKENS=4096
      - MAX_BATCH_TOTAL_TOKENS=8192
      - MAX_WAITING_TOKENS=20
      - VALIDATION_WORKERS=2
      - JSON_OUTPUT=true
      - OTLP_ENDPOINT=http://jaeger:14268/api/traces
    volumes:
      - tgi_data:/data
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Optional: Jaeger for tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: bible-companion-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    restart: unless-stopped

volumes:
  tgi_data:
    driver: local

networks:
  default:
    name: bible-companion-network
