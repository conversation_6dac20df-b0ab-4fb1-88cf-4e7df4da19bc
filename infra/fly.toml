app = "bible-gemma"
primary_region = "iad"

[build]
image = "ghcr.io/huggingface/text-generation-inference:latest"

[env]
MODEL_ID = "google/gemma-2b-it"
MAX_CONCURRENT_REQUESTS = "128"
MAX_BEST_OF = "2"
MAX_STOP_SEQUENCES = "4"
MAX_INPUT_LENGTH = "1024"
MAX_TOTAL_TOKENS = "2048"
WAITING_SERVED_RATIO = "1.2"
MAX_BATCH_PREFILL_TOKENS = "4096"
MAX_BATCH_TOTAL_TOKENS = "8192"
MAX_WAITING_TOKENS = "20"
VALIDATION_WORKERS = "2"
JSON_OUTPUT = "true"

[[services]]
internal_port = 8080
processes = ["app"]

# GPU autoscaling configuration
auto_start_machines = true   # start on first TCP hit
auto_stop_machines = true    # stop when idle
min_machines_running = 0     # cost-saving: no machines running when idle

# Stop VM after 10 minutes of no requests (600 seconds)
stop_timeout = 600

[services.concurrency]
hard_limit = 20
soft_limit = 15
type = "connections"

[[services.ports]]
port = 80
handlers = ["http"]

[[services.ports]]
port = 443
handlers = ["tls", "http"]

[services.http_checks]
interval = "10s"
grace_period = "5s"
method = "GET"
path = "/health"
protocol = "http"
timeout = "2s"
tls_skip_verify = false

[services.tcp_checks]
interval = "15s"
timeout = "2s"
grace_period = "1s"

# GPU machine configuration
[[vm]]
size = "a100-40gb"
