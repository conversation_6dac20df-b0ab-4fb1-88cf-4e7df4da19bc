/**
 * Tests for voice service functionality.
 */

import { voiceService } from '../voice';
import { auth } from '../../config/firebase';

// Mock Firebase auth
jest.mock('../../config/firebase', () => ({
  auth: {
    currentUser: null,
  },
}));

// Mock global objects
const mockMediaDevices = {
  getUserMedia: jest.fn(),
};

const mockMediaRecorder = jest.fn().mockImplementation(() => ({
  start: jest.fn(),
  stop: jest.fn(),
  ondataavailable: null,
  stream: {
    getTracks: () => [{ stop: jest.fn() }],
  },
  state: 'inactive',
}));

const mockAudioContext = jest.fn().mockImplementation(() => ({
  createAnalyser: jest.fn(() => ({
    fftSize: 256,
    frequencyBinCount: 128,
    getByteFrequencyData: jest.fn(),
    connect: jest.fn(),
  })),
  createMediaStreamSource: jest.fn(() => ({
    connect: jest.fn(),
  })),
  close: jest.fn(),
}));

const mockWebSocket = jest.fn().mockImplementation(() => ({
  send: jest.fn(),
  close: jest.fn(),
  readyState: WebSocket.OPEN,
  onopen: null,
  onmessage: null,
  onclose: null,
  onerror: null,
}));

// Setup global mocks
Object.defineProperty(global, 'navigator', {
  value: {
    mediaDevices: mockMediaDevices,
  },
  writable: true,
});

Object.defineProperty(global, 'MediaRecorder', {
  value: mockMediaRecorder,
  writable: true,
});

Object.defineProperty(global, 'AudioContext', {
  value: mockAudioContext,
  writable: true,
});

Object.defineProperty(global, 'WebSocket', {
  value: mockWebSocket,
  writable: true,
});

Object.defineProperty(global, 'fetch', {
  value: jest.fn(),
  writable: true,
});

Object.defineProperty(global, 'Audio', {
  value: jest.fn().mockImplementation(() => ({
    play: jest.fn().mockResolvedValue(undefined),
    onended: null,
    onerror: null,
  })),
  writable: true,
});

Object.defineProperty(global, 'URL', {
  value: {
    createObjectURL: jest.fn(() => 'blob:mock-url'),
    revokeObjectURL: jest.fn(),
  },
  writable: true,
});

describe('VoiceService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (auth as any).currentUser = null;
  });

  describe('isVoiceSupported', () => {
    it('should return true when all required APIs are available', () => {
      expect(voiceService.isVoiceSupported()).toBe(true);
    });

    it('should return false when MediaRecorder is not available', () => {
      delete (global as any).MediaRecorder;
      expect(voiceService.isVoiceSupported()).toBe(false);
    });
  });

  describe('checkPremiumAccess', () => {
    it('should return false when user is not authenticated', async () => {
      const result = await voiceService.checkPremiumAccess();
      expect(result).toBe(false);
    });

    it('should return true when user has premium claim', async () => {
      const mockUser = {
        getIdTokenResult: jest.fn().mockResolvedValue({
          claims: { premium: true },
        }),
      };
      (auth as any).currentUser = mockUser;

      const result = await voiceService.checkPremiumAccess();
      expect(result).toBe(true);
    });

    it('should return false when user does not have premium claim', async () => {
      const mockUser = {
        getIdTokenResult: jest.fn().mockResolvedValue({
          claims: { premium: false },
        }),
      };
      (auth as any).currentUser = mockUser;

      const result = await voiceService.checkPremiumAccess();
      expect(result).toBe(false);
    });
  });

  describe('textToSpeech', () => {
    beforeEach(() => {
      const mockUser = {
        getIdToken: jest.fn().mockResolvedValue('mock-token'),
        getIdTokenResult: jest.fn().mockResolvedValue({
          claims: { premium: true },
        }),
      };
      (auth as any).currentUser = mockUser;
    });

    it('should successfully convert text to speech', async () => {
      const mockBlob = new Blob(['audio data'], { type: 'audio/opus' });
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        blob: () => Promise.resolve(mockBlob),
      });

      const result = await voiceService.textToSpeech({
        text: 'Hello world',
      });

      expect(result).toBe('blob:mock-url');
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/v1/speech/tts'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token',
          }),
          body: JSON.stringify({
            text: 'Hello world',
            voice: 'english_female',
            language: 'en',
          }),
        }),
      );
    });

    it('should throw error when user is not premium', async () => {
      const mockUser = {
        getIdTokenResult: jest.fn().mockResolvedValue({
          claims: { premium: false },
        }),
      };
      (auth as any).currentUser = mockUser;

      await expect(voiceService.textToSpeech({
        text: 'Hello world',
      })).rejects.toThrow('Premium subscription required');
    });

    it('should throw error when API returns 402', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 402,
        statusText: 'Payment Required',
      });

      await expect(voiceService.textToSpeech({
        text: 'Hello world',
      })).rejects.toThrow('Premium subscription required');
    });
  });

  describe('playAudio', () => {
    it('should play audio successfully', async () => {
      const mockAudio = {
        play: jest.fn().mockResolvedValue(undefined),
        onended: null,
        onerror: null,
      };
      (global.Audio as jest.Mock).mockReturnValue(mockAudio);

      const promise = voiceService.playAudio('blob:mock-url');

      // Simulate audio ended
      if (mockAudio.onended) {
        mockAudio.onended();
      }

      await expect(promise).resolves.toBeUndefined();
      expect(mockAudio.play).toHaveBeenCalled();
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-url');
    });

    it('should handle audio play error', async () => {
      const mockAudio = {
        play: jest.fn().mockRejectedValue(new Error('Play failed')),
        onended: null,
        onerror: null,
      };
      (global.Audio as jest.Mock).mockReturnValue(mockAudio);

      await expect(voiceService.playAudio('blob:mock-url')).rejects.toThrow('Play failed');
    });
  });

  describe('startRecording', () => {
    beforeEach(() => {
      const mockUser = {
        getIdToken: jest.fn().mockResolvedValue('mock-token'),
        getIdTokenResult: jest.fn().mockResolvedValue({
          claims: { premium: true },
        }),
      };
      (auth as any).currentUser = mockUser;

      mockMediaDevices.getUserMedia.mockResolvedValue({
        getTracks: () => [{ stop: jest.fn() }],
      });
    });

    it('should throw error when voice is not supported', async () => {
      delete (global as any).MediaRecorder;

      await expect(voiceService.startRecording(
        jest.fn(),
        jest.fn(),
        jest.fn(),
      )).rejects.toThrow('Voice recording not supported');
    });

    it('should throw error when user is not premium', async () => {
      const mockUser = {
        getIdTokenResult: jest.fn().mockResolvedValue({
          claims: { premium: false },
        }),
      };
      (auth as any).currentUser = mockUser;

      await expect(voiceService.startRecording(
        jest.fn(),
        jest.fn(),
        jest.fn(),
      )).rejects.toThrow('Premium subscription required');
    });
  });
});
