/**
 * Text-to-speech button for playing AI responses.
 * Premium feature using XTTS.
 */

import React, { useState } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  Alert,
  Animated,
  View,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../hooks/useTheme';
import { useVoice } from '../../hooks/useVoice';

interface TTSButtonProps {
  text: string;
  disabled?: boolean;
  size?: 'small' | 'medium';
  voice?: string;
  language?: string;
}

export const TTSButton: React.FC<TTSButtonProps> = ({
  text,
  disabled = false,
  size = 'small',
  voice = 'english_female',
  language = 'en',
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { speakText, stopSpeaking, isSpeaking, isPremium, checkPermissions } = useVoice();

  const [isLoading, setIsLoading] = useState(false);
  const spinValue = new Animated.Value(0);

  React.useEffect(() => {
    checkPermissions();
  }, [checkPermissions]);

  React.useEffect(() => {
    if (isSpeaking) {
      // Start spinning animation
      Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ).start();
    } else {
      // Stop animation
      spinValue.setValue(0);
    }
  }, [isSpeaking, spinValue]);

  const handlePress = async () => {
    if (!text.trim()) {
      return;
    }

    if (!isPremium) {
      Alert.alert(
        t('voice.premiumRequired'),
        t('voice.premiumRequiredMessage'),
        [
          { text: t('cancel'), style: 'cancel' },
          { text: t('voice.upgradeToPremium'), onPress: () => {
            // TODO: Navigate to premium upgrade screen
          } },
        ],
      );
      return;
    }

    try {
      if (isSpeaking) {
        stopSpeaking();
      } else {
        setIsLoading(true);
        await speakText(text, { voice, language });
      }
    } catch (error) {
      console.error('TTS error:', error);
      Alert.alert(
        t('error'),
        error instanceof Error ? error.message : t('voice.ttsError'),
        [{ text: t('ok') }],
      );
    } finally {
      setIsLoading(false);
    }
  };

  const getButtonSize = () => {
    return size === 'small' ? 28 : 36;
  };

  const getIconSize = () => {
    return size === 'small' ? 12 : 16;
  };

  const buttonSize = getButtonSize();
  const iconSize = getIconSize();

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const renderIcon = () => {
    if (isLoading) {
      return (
        <Animated.View style={{ transform: [{ rotate: spin }] }}>
          <View style={[styles.loadingIcon, {
            width: iconSize,
            height: iconSize,
            borderColor: theme.colors.primary,
            borderTopColor: 'transparent',
          }]} />
        </Animated.View>
      );
    }

    if (isSpeaking) {
      return (
        <View style={[styles.stopIcon, {
          width: iconSize * 0.7,
          height: iconSize * 0.7,
          backgroundColor: theme.colors.primary,
        }]} />
      );
    }

    return (
      <View style={styles.speakerIcon}>
        <View style={[styles.speakerBase, {
          width: iconSize * 0.5,
          height: iconSize * 0.7,
          backgroundColor: theme.colors.primary,
        }]} />
        <View style={[styles.speakerCone, {
          width: iconSize * 0.3,
          height: iconSize * 0.5,
          borderLeftColor: theme.colors.primary,
          left: iconSize * 0.5,
        }]} />
        <View style={[styles.soundWave1, {
          width: iconSize * 0.15,
          height: iconSize * 0.15,
          borderColor: theme.colors.primary,
          right: -iconSize * 0.1,
          top: iconSize * 0.15,
        }]} />
        <View style={[styles.soundWave2, {
          width: iconSize * 0.2,
          height: iconSize * 0.2,
          borderColor: theme.colors.primary,
          right: -iconSize * 0.15,
          top: iconSize * 0.1,
        }]} />
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={[
        styles.ttsButton,
        {
          width: buttonSize,
          height: buttonSize,
          backgroundColor: isSpeaking ? theme.colors.secondary : theme.colors.surface,
          borderColor: theme.colors.border,
          opacity: disabled ? 0.5 : 1,
        },
      ]}
      onPress={handlePress}
      disabled={disabled || isLoading}
      activeOpacity={0.7}
    >
      {renderIcon()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  ttsButton: {
    borderRadius: 14,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
  },
  loadingIcon: {
    borderWidth: 1.5,
    borderRadius: 8,
    borderStyle: 'solid',
  },
  stopIcon: {
    borderRadius: 1,
  },
  speakerIcon: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  speakerBase: {
    borderTopLeftRadius: 2,
    borderBottomLeftRadius: 2,
  },
  speakerCone: {
    position: 'absolute',
    borderLeftWidth: 4,
    borderTopWidth: 2,
    borderBottomWidth: 2,
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
    borderRightColor: 'transparent',
  },
  soundWave1: {
    position: 'absolute',
    borderWidth: 1,
    borderRadius: 8,
    borderStyle: 'solid',
  },
  soundWave2: {
    position: 'absolute',
    borderWidth: 1,
    borderRadius: 10,
    borderStyle: 'solid',
  },
});
