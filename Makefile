# Bible Companion - Makefile
# Development and deployment commands for the Bible Companion project

.PHONY: help install dev test lint format clean docker-build docker-run fly-deploy-gpu

# Default target
help:
	@echo "Bible Companion - Available Commands"
	@echo "===================================="
	@echo ""
	@echo "Development:"
	@echo "  install          Install dependencies"
	@echo "  dev              Start development server"
	@echo "  test             Run tests"
	@echo "  lint             Run linting"
	@echo "  format           Format code"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build     Build Docker image"
	@echo "  docker-run       Run Docker container"
	@echo ""
	@echo "Deployment:"
	@echo "  fly-deploy-gpu   Deploy GPU service to Fly.io"
	@echo ""
	@echo "Utilities:"
	@echo "  clean            Clean build artifacts"

# Development commands
install:
	@echo "Installing dependencies..."
	cd backend && pip install -r requirements.txt
	npm install

dev:
	@echo "Starting development server..."
	cd backend && uvicorn main:app --reload --host 0.0.0.0 --port 8000

test:
	@echo "Running tests..."
	cd backend && python -m pytest tests/ -v

lint:
	@echo "Running linting..."
	cd backend && flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	cd backend && mypy . --ignore-missing-imports

format:
	@echo "Formatting code..."
	cd backend && black .
	cd backend && isort .
	npx prettier --write "**/*.{js,jsx,ts,tsx,json,md}"

# Docker commands
docker-build:
	@echo "Building Docker image..."
	cd backend && docker build -t bible-companion-backend .

docker-run:
	@echo "Running Docker container..."
	docker run -p 8000:8000 --env-file backend/.env bible-companion-backend

# Fly.io deployment
fly-deploy-gpu:
	@echo "Deploying GPU service to Fly.io..."
	@echo "Using configuration: infra/fly.toml"
	fly deploy -c infra/fly.toml

# Utility commands
clean:
	@echo "Cleaning build artifacts..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	rm -rf backend/dist/ backend/build/ 2>/dev/null || true

# TGI local development
tgi-start:
	@echo "Starting TGI server locally..."
	cd infra/tgi && docker compose up -d

tgi-stop:
	@echo "Stopping TGI server..."
	cd infra/tgi && docker compose down

tgi-logs:
	@echo "Showing TGI logs..."
	cd infra/tgi && docker compose logs -f

# GPU warm-up for testing
warm-up:
	@echo "Starting GPU warm-up service..."
	@echo "Make sure to set API_URL and TEST_JWT environment variables"
	cd backend && python scripts/warm_up.py

# Infrastructure commands
infra-setup:
	@echo "Setting up infrastructure..."
	cd infrastructure && ./setup.sh

infra-deploy:
	@echo "Deploying infrastructure..."
	cd infrastructure && pulumi up

# Database commands
db-migrate:
	@echo "Running database migrations..."
	cd backend && python run_neo4j_migrations.py

db-seed:
	@echo "Seeding database..."
	cd backend && python scripts/seed_bc_graph.py

# Monitoring
metrics:
	@echo "Starting metrics collection..."
	@echo "Prometheus metrics available at http://localhost:9091/metrics"

# Quick development setup
setup: install tgi-start db-migrate
	@echo "Development environment setup complete!"
	@echo "Run 'make dev' to start the development server"

# Production deployment checklist
deploy-check:
	@echo "Pre-deployment checklist:"
	@echo "✓ Tests passing: make test"
	@echo "✓ Linting clean: make lint"
	@echo "✓ Environment variables set"
	@echo "✓ Fly.io secrets configured"
	@echo "✓ GPU quota available"
	@echo ""
	@echo "Deploy with: make fly-deploy-gpu"
