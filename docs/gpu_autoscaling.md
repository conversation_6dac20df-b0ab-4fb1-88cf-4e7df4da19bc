# 🚀 GPU Autoscaling on Fly.io

This document explains the GPU autoscaling configuration for the Bible Companion Gemma TGI service on Fly.io, including cost optimization, monitoring, and troubleshooting.

## 📋 Overview

The Gemma TGI (Text Generation Inference) service is deployed on Fly.io with autoscaling capabilities to minimize costs while maintaining performance. The service automatically:

- **Starts** GPU machines on the first incoming request
- **Stops** GPU machines after 10 minutes of inactivity
- **Scales to zero** when idle to eliminate costs

## 💰 Cost Analysis

### Traditional Always-On Deployment
- **A100-40GB GPU**: ~$0.35/hour
- **Monthly cost**: $252 (24/7 operation)
- **Idle time waste**: Significant for development/staging

### Autoscaling Deployment
- **Active time only**: $0.35/hour when processing requests
- **Idle time**: $0.00/hour (machine stopped)
- **Cold start penalty**: ~22 seconds startup time
- **Estimated savings**: 60-80% for typical development workloads

### Example Scenarios

| Usage Pattern | Traditional Cost | Autoscaling Cost | Savings |
|---------------|------------------|------------------|---------|
| 8 hours/day development | $252/month | $84/month | 67% |
| 2 hours/day testing | $252/month | $21/month | 92% |
| Production (16h/day) | $252/month | $168/month | 33% |

## ⚙️ Configuration

### Fly.io Configuration (`infra/fly.toml`)

```toml
[[services]]
internal_port = 8080
processes = ["app"]

# GPU autoscaling configuration
auto_start_machines = true   # start on first TCP hit
auto_stop_machines = true    # stop when idle
min_machines_running = 0     # cost-saving: no machines running when idle

# Stop VM after 10 minutes of no requests (600 seconds)
stop_timeout = 600

[services.concurrency]
hard_limit = 20
soft_limit = 15
type = "connections"
```

### Key Parameters

- **`auto_start_machines`**: Automatically starts machines on incoming traffic
- **`auto_stop_machines`**: Automatically stops machines when idle
- **`min_machines_running`**: Set to 0 for maximum cost savings
- **`stop_timeout`**: Time in seconds before stopping idle machines (600s = 10 minutes)

## 🔧 Tuning Stop Timeout

The `stop_timeout` parameter controls how long machines stay running after the last request:

### Recommended Values

| Use Case | Timeout | Rationale |
|----------|---------|-----------|
| Development | 300s (5 min) | Faster iteration, acceptable cold starts |
| Staging/QA | 600s (10 min) | Balance between cost and convenience |
| Production | 1800s (30 min) | Minimize cold starts for users |

### Adjusting Timeout

Edit `infra/fly.toml`:

```toml
# For development (more aggressive cost saving)
stop_timeout = 300

# For production (fewer cold starts)
stop_timeout = 1800
```

Then redeploy:

```bash
make fly-deploy-gpu
```

## 📊 Monitoring & Alerts

### Prometheus Metrics

The service exposes the following metrics:

- **`gpu_cold_start_total`**: Counter of GPU machine cold starts
- **`chat_requests_total`**: Total chat requests by status and model
- **`chat_request_duration_seconds`**: Chat request duration histogram
- **`tgi_response_time_seconds`**: TGI service response time

### Grafana Alert Rules

Add this alert rule to monitor cold starts:

```yaml
groups:
  - name: bible-companion-gpu
    rules:
      - alert: FlyGPUColdStart
        expr: increase(gpu_cold_start_total[5m]) > 0
        for: 0m
        labels:
          severity: warning
          service: bible-companion-tgi
        annotations:
          summary: "GPU cold-start occurred"
          description: "A GPU machine cold start was detected. Response time may be elevated for ~22 seconds."
          runbook_url: "https://github.com/Jpkay/bible/blob/main/docs/gpu_autoscaling.md#troubleshooting"
```

### Setting Up Slack Alerts

For production, configure Slack notifications via Grafana Cloud:

1. Create a Slack webhook URL
2. Add notification channel in Grafana
3. Link the `FlyGPUColdStart` alert to the channel

## 🧪 Testing & Warm-up

### Manual Testing

Test the autoscaling behavior:

```bash
# 1. Verify machine is stopped
fly status --app bible-gemma

# 2. Send first request (triggers cold start)
curl -X POST https://bible-gemma.fly.dev/v1/chat \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"threadId": "test", "prompt": "Hello"}'

# 3. Check machine status
fly status --app bible-gemma

# 4. Wait 10+ minutes and verify machine stops
```

### Warm-up Script

For QA testing, use the warm-up script to keep machines active:

```bash
# Set environment variables
export API_URL="https://bible-gemma.fly.dev"
export TEST_JWT="your-test-jwt-token"
export WARM_UP_INTERVAL="1800"  # 30 minutes

# Run warm-up service
python backend/scripts/warm_up.py
```

The script will:
- Send a request every 30 minutes
- Keep the GPU machine warm during testing
- Log all activities for monitoring

## 🚨 Troubleshooting

### Cold Start Performance

**Symptom**: First request takes 20-25 seconds
**Cause**: GPU machine startup time
**Solutions**:
- Increase `stop_timeout` for frequently used environments
- Use warm-up script during active development
- Consider keeping 1 machine running for production (`min_machines_running = 1`)

### Frequent Cold Starts

**Symptom**: Multiple cold starts per hour
**Cause**: `stop_timeout` too aggressive for usage pattern
**Solutions**:
- Increase `stop_timeout` to match usage patterns
- Monitor request frequency and adjust accordingly
- Use warm-up script for predictable usage

### High Costs

**Symptom**: Costs higher than expected
**Cause**: Machines not stopping due to persistent connections
**Solutions**:
- Check for connection leaks in application code
- Verify `auto_stop_machines = true` in configuration
- Monitor machine status with `fly status`

### Deployment Issues

**Symptom**: Deployment fails or machines don't start
**Solutions**:
- Verify GPU quota with Fly.io support
- Check machine size availability in region
- Review logs with `fly logs --app bible-gemma`

## 📚 Additional Resources

- [Fly.io Autoscaling Documentation](https://fly.io/docs/apps/autostart-stop/)
- [TGI Performance Tuning](https://huggingface.co/docs/text-generation-inference/basic_tutorials/launcher)
- [Prometheus Monitoring Setup](https://prometheus.io/docs/introduction/overview/)

## 🔄 Deployment Commands

```bash
# Deploy GPU service
make fly-deploy-gpu

# Check status
fly status --app bible-gemma

# View logs
fly logs --app bible-gemma

# Scale manually (if needed)
fly scale count 1 --app bible-gemma

# Stop all machines
fly scale count 0 --app bible-gemma
```
