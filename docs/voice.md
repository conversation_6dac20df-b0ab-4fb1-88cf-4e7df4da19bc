# Voice Features Documentation

Bible Companion includes premium voice features that enable natural speech interaction with the AI assistant. Users can speak their questions and hear AI responses using state-of-the-art speech recognition and synthesis.

## Overview

The voice system consists of three main components:

1. **Speech-to-Text (STT)** - Converts user speech to text using Whisper.cpp
2. **Text-to-Speech (TTS)** - Converts AI responses to natural speech using XTTS
3. **Premium Gating** - Restricts voice features to premium subscribers

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   FastAPI        │    │   AI Services   │
│                 │    │   Backend        │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ VoiceButton │ │◄──►│ │ /speech/stt  │ │◄──►│ │ Whisper.cpp │ │
│ │             │ │    │ │ (WebSocket)  │ │    │ │             │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ TTSButton   │ │◄──►│ │ /speech/tts  │ │◄──►│ │    XTTS     │ │
│ │             │ │    │ │ (REST)       │ │    │ │             │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Features

### Speech-to-Text (STT)

- **Real-time transcription** with partial and final results
- **WebSocket streaming** for low-latency interaction
- **Waveform visualization** during recording
- **Automatic silence detection** and processing
- **High accuracy** using Whisper tiny-int8 model

### Text-to-Speech (TTS)

- **Natural voice synthesis** using XTTS v2
- **Multiple voice options** (currently English female)
- **Streaming audio delivery** in Opus format
- **Automatic audio playback** with error handling
- **Configurable voice parameters**

### Premium Gating

- **Firebase custom claims** validation (`premium: true`)
- **402 Payment Required** responses for non-premium users
- **Graceful degradation** with upgrade prompts
- **Usage tracking** and rate limiting

## API Endpoints

### WebSocket: `/v1/speech/stt`

Real-time speech-to-text conversion.

**Authentication**: Premium Firebase JWT token required

**Protocol**:
- Client sends binary audio chunks (Ogg format)
- Server responds with JSON messages

**Request**: Binary audio data (Ogg/WebM)

**Response**:
```json
{
  "type": "partial|final|error",
  "text": "transcribed text",
  "confidence": 0.95
}
```

**Example Usage**:
```javascript
const ws = new WebSocket('/v1/speech/stt?token=jwt_token');
ws.onmessage = (event) => {
  const response = JSON.parse(event.data);
  if (response.type === 'final') {
    console.log('Final transcript:', response.text);
  }
};
```

### POST: `/v1/speech/tts`

Convert text to speech.

**Authentication**: Premium Firebase JWT token required

**Request**:
```json
{
  "text": "Hello, how can I help you today?",
  "voice": "english_female",
  "language": "en"
}
```

**Response**: Audio stream (audio/opus)

**Example Usage**:
```javascript
const response = await fetch('/v1/speech/tts', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer jwt_token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ text: 'Hello world' })
});

const audioBlob = await response.blob();
const audioUrl = URL.createObjectURL(audioBlob);
const audio = new Audio(audioUrl);
audio.play();
```

### GET: `/v1/speech/health`

Check health status of speech services.

**Response**:
```json
{
  "status": "healthy|degraded",
  "services": {
    "whisper": {
      "status": "healthy|unhealthy",
      "url": "http://whisper:9000/health"
    },
    "xtts": {
      "status": "healthy|unhealthy", 
      "url": "http://xtts:9100/health"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Frontend Components

### VoiceButton

Interactive recording button with waveform visualization.

**Props**:
- `onTranscript: (text: string) => void` - Callback for final transcript
- `disabled?: boolean` - Disable recording
- `size?: 'small' | 'medium' | 'large'` - Button size

**Features**:
- Animated recording indicator
- Real-time waveform display
- Error handling and user feedback
- Premium access validation

### TTSButton

Button to play AI responses as speech.

**Props**:
- `text: string` - Text to convert to speech
- `disabled?: boolean` - Disable TTS
- `size?: 'small' | 'medium'` - Button size
- `voice?: string` - Voice selection
- `language?: string` - Language code

**Features**:
- Loading and playing states
- Audio playback controls
- Error handling
- Premium access validation

## Infrastructure

### Whisper.cpp Service

**Docker Image**: `ghcr.io/ggerganov/whisper.cpp:tiny-int8`
**Port**: 9000
**Model**: tiny-int8.en (optimized for English)

**Configuration**:
```yaml
services:
  whisper:
    image: ghcr.io/ggerganov/whisper.cpp:tiny-int8
    ports:
      - "9000:9000"
    command: ["--port", "9000", "--model", "tiny-int8.en"]
```

### XTTS Service

**Docker Image**: `ghcr.io/coqui-ai/xtts:latest`
**Port**: 9100
**Model**: tts_models/multilingual/multi-dataset/xtts_v2

**Configuration**:
```yaml
services:
  xtts:
    image: ghcr.io/coqui-ai/xtts:latest
    ports:
      - "9100:9100"
    environment:
      - VOICE=english_female
      - MODEL_NAME=tts_models/multilingual/multi-dataset/xtts_v2
```

## Premium Features

Voice functionality is restricted to premium subscribers through Firebase custom claims.

### Setting Premium Status

```javascript
// Admin SDK (server-side)
await admin.auth().setCustomUserClaims(uid, { premium: true });
```

### Checking Premium Status

```javascript
// Client-side
const token = await user.getIdTokenResult();
const isPremium = token.claims.premium === true;
```

## Error Handling

### Common Error Codes

- **402 Payment Required**: User needs premium subscription
- **400 Bad Request**: Invalid text input (empty or too long)
- **503 Service Unavailable**: Speech services are down
- **401 Unauthorized**: Invalid or missing authentication

### Error Messages

The system provides user-friendly error messages in multiple languages:

```json
{
  "voice": {
    "premiumRequired": "Premium Required",
    "premiumRequiredMessage": "Voice features require a premium subscription.",
    "notSupported": "Voice Not Supported",
    "recordingError": "Recording failed. Please try again."
  }
}
```

## Browser Compatibility

### Supported Browsers

- **Chrome/Chromium**: Full support
- **Firefox**: Full support  
- **Safari**: Full support (iOS 14.3+)
- **Edge**: Full support

### Required APIs

- `MediaDevices.getUserMedia()` - Microphone access
- `MediaRecorder` - Audio recording
- `WebSocket` - Real-time communication
- `AudioContext` - Waveform analysis
- `Audio` - Playback

### Graceful Degradation

When voice features are not supported:
- Voice buttons are hidden
- Text input remains available
- User receives informative messages

## Performance Considerations

### Audio Processing

- **Chunk size**: 1-second audio chunks for real-time processing
- **Sample rate**: 16kHz for optimal Whisper performance
- **Format**: Ogg/WebM with Opus codec for compression

### Network Optimization

- **Streaming**: Audio is streamed in chunks to reduce latency
- **Compression**: Opus codec provides excellent compression
- **Caching**: Audio responses can be cached for repeated phrases

### Resource Usage

- **Memory**: ~100MB for Whisper tiny model
- **CPU**: Optimized for real-time processing
- **Bandwidth**: ~32kbps for audio streaming

## Testing

### Unit Tests

```bash
# Frontend tests
npm test src/services/__tests__/voice.test.ts

# Backend tests  
pytest backend/tests/test_speech_api.py
```

### Integration Tests

```bash
# Full voice workflow
pytest backend/tests/test_speech_api.py::TestSpeechIntegration::test_full_voice_workflow
```

### Manual Testing

1. **STT Testing**: Record audio and verify transcription accuracy
2. **TTS Testing**: Send text and verify audio quality
3. **Premium Gating**: Test with premium and non-premium users
4. **Error Handling**: Test network failures and service outages

## Deployment

### Docker Compose

```bash
# Start speech services
cd infra/whisper && docker-compose up -d
cd infra/xtts && docker-compose up -d

# Verify services
curl http://localhost:9000/health
curl http://localhost:9100/health
```

### Environment Variables

```bash
# Backend
WHISPER_URL=http://whisper:9000
XTTS_URL=http://xtts:9100

# Frontend
EXPO_PUBLIC_API_BASE_URL=https://api.biblecompanion.app
```

## Monitoring

### Health Checks

- **Whisper**: `GET /health` endpoint
- **XTTS**: `GET /health` endpoint  
- **Combined**: `GET /v1/speech/health` endpoint

### Metrics

- Transcription accuracy rates
- Audio generation latency
- Error rates by service
- Premium user adoption

### Logging

```json
{
  "timestamp": "2024-01-01T00:00:00Z",
  "level": "INFO",
  "message": "STT WebSocket connected",
  "user_id": "user123",
  "service": "speech"
}
```

## Future Enhancements

### Planned Features

- **Multi-language support** (French, Kinyarwanda, Swahili)
- **Voice selection** (male/female options)
- **Conversation mode** (continuous listening)
- **Offline support** (local models)

### Performance Improvements

- **Model optimization** (quantization, pruning)
- **Edge deployment** (CDN distribution)
- **Caching strategies** (response caching)
- **Load balancing** (multiple service instances)
