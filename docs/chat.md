# Chat API Documentation

The Bible Companion Chat API provides AI-powered conversational capabilities using the Gemma 2B model through Text Generation Inference (TGI). The API integrates with user reading history to provide contextually relevant biblical guidance.

## Overview

- **Model**: Google Gemma 2B Instruct
- **Authentication**: Firebase JWT tokens
- **Rate Limiting**: 60 requests per minute per user
- **Context**: Recent Bible verses read by the user
- **Storage**: MongoDB for conversation history

## Endpoints

### POST /v1/chat

Create a chat message and receive an AI response.

**Authentication**: Required (Firebase JWT)  
**Rate Limit**: 60 requests/minute

#### Request Body

```json
{
  "thread_id": "optional-thread-id",
  "prompt": "What does the Bible say about faith?",
  "include_context": true,
  "max_tokens": 512,
  "temperature": 0.7
}
```

#### Parameters

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `thread_id` | string | No | Existing thread ID (creates new if omitted) |
| `prompt` | string | Yes | User's question/message (1-2000 chars) |
| `include_context` | boolean | No | Include recent verses in context (default: true) |
| `max_tokens` | integer | No | Max response tokens (1-2048, default: 512) |
| `temperature` | float | No | Response creativity (0.0-2.0, default: 0.7) |

#### Response

```json
{
  "thread_id": "thread-12345",
  "answer": "Faith, according to Hebrews 11:1, is confidence in what we hope for...",
  "cursor": "msg-67890",
  "context_verses": [
    {
      "book": "Hebrews",
      "chapter": 11,
      "verse": 1,
      "text": "Now faith is confidence in what we hope for...",
      "translation": "NIV",
      "read_at": "2024-01-15T10:30:00Z"
    }
  ],
  "metadata": {
    "model": "gemma-2b-it",
    "tokens_used": 245,
    "context_verses_count": 3
  }
}
```

#### Error Responses

| Status | Description |
|--------|-------------|
| 400 | Invalid request parameters |
| 401 | Authentication required |
| 429 | Rate limit exceeded |
| 500 | Internal server error |
| 502 | TGI service unavailable |

### GET /v1/chat/threads

Get user's chat threads.

**Authentication**: Required  
**Rate Limit**: 30 requests/minute

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `limit` | integer | 20 | Max threads to return (1-100) |
| `offset` | integer | 0 | Number of threads to skip |

#### Response

```json
{
  "threads": [
    {
      "id": "thread-123",
      "user_id": "user-456",
      "title": "Questions about Faith",
      "created_at": "2024-01-15T09:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "is_active": true
    }
  ],
  "limit": 20,
  "offset": 0,
  "has_more": false
}
```

### GET /v1/chat/threads/{thread_id}/messages

Get messages from a specific thread.

**Authentication**: Required  
**Rate Limit**: 60 requests/minute

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `thread_id` | string | Chat thread ID |

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `limit` | integer | 50 | Max messages to return (1-100) |
| `cursor` | string | null | Pagination cursor (message ID) |

#### Response

```json
{
  "messages": [
    {
      "id": "msg-123",
      "role": "user",
      "content": "What is faith?",
      "timestamp": "2024-01-15T10:00:00Z"
    },
    {
      "id": "msg-124",
      "role": "assistant",
      "content": "Faith is confidence in what we hope for...",
      "timestamp": "2024-01-15T10:00:05Z"
    }
  ],
  "thread_id": "thread-123",
  "limit": 50,
  "has_more": false,
  "next_cursor": null
}
```

### DELETE /v1/chat/threads/{thread_id}

Delete a chat thread (soft delete).

**Authentication**: Required  
**Rate Limit**: 10 requests/minute

#### Response

```json
{
  "message": "Thread deleted successfully",
  "thread_id": "thread-123"
}
```

## Context Integration

The chat API automatically includes context from the user's recent Bible reading:

1. **Neo4j Query**: Retrieves last 20 verses read by the user
2. **Context Injection**: Adds verse references to the AI prompt
3. **Relevant Responses**: AI provides biblically-informed answers

### Example Context

```
Recent verses the user has been reading:
- John 3:16 (NIV) - For God so loved the world that he gave his one and only Son...
- Romans 8:28 (NIV) - And we know that in all things God works for the good...
- Philippians 4:13 (NIV) - I can do all this through him who gives me strength.
```

## Authentication

All endpoints require a valid Firebase JWT token in the Authorization header:

```http
Authorization: Bearer <firebase-jwt-token>
```

The token is validated against your Firebase project and must contain:
- Valid `uid` (user ID)
- Non-expired timestamp
- Proper signature

## Rate Limiting

Rate limits are enforced per user (based on JWT `uid`):

| Endpoint | Limit |
|----------|-------|
| POST /v1/chat | 60/minute |
| GET /v1/chat/threads | 30/minute |
| GET /v1/chat/threads/{id}/messages | 60/minute |
| DELETE /v1/chat/threads/{id} | 10/minute |

When rate limit is exceeded, the API returns:

```json
{
  "error": "Rate limit exceeded",
  "retry_after": 60
}
```

## Error Handling

The API uses standard HTTP status codes and returns structured error responses:

```json
{
  "error": "Error description",
  "type": "ErrorType",
  "details": {
    "field": "Additional context"
  }
}
```

### Common Error Types

- `AuthenticationError`: Invalid or missing JWT token
- `ValidationError`: Invalid request parameters
- `TGIError`: Text generation service issues
- `DatabaseError`: Database operation failures
- `RateLimitError`: Rate limit exceeded

## SDK Examples

### JavaScript/TypeScript

```typescript
const response = await fetch('/v1/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${firebaseToken}`
  },
  body: JSON.stringify({
    prompt: 'What does the Bible say about love?',
    include_context: true,
    max_tokens: 300
  })
});

const data = await response.json();
console.log(data.answer);
```

### Python

```python
import requests

response = requests.post(
    'https://api.biblecompanion.com/v1/chat',
    headers={
        'Authorization': f'Bearer {firebase_token}',
        'Content-Type': 'application/json'
    },
    json={
        'prompt': 'How can I grow in faith?',
        'include_context': True
    }
)

data = response.json()
print(data['answer'])
```

## Best Practices

1. **Context Usage**: Enable `include_context` for more relevant responses
2. **Token Management**: Refresh Firebase tokens before expiration
3. **Error Handling**: Implement retry logic for 5xx errors
4. **Rate Limiting**: Implement client-side rate limiting
5. **Thread Management**: Reuse threads for related conversations
6. **Security**: Never expose Firebase tokens in client-side code
