# Neo4j Setup & Migration Guide

This guide covers setting up Neo4j for the Bible Companion project and managing schema migrations.

## 📋 Overview

The Bible Companion project uses Neo4j as a graph database to model relationships between:
- **Users** and their reading patterns
- **Verses** and their connections
- **Circles** (study groups) and their members
- **Reading relationships** and social interactions

## 🚀 Quick Start

### 1. Neo4j Aura Setup

1. **Create Neo4j Aura Account**
   - Go to [Neo4j Aura](https://neo4j.com/cloud/aura/)
   - Sign up for a free account
   - Create a new AuraDB Free instance

2. **Get Connection Details**
   - Copy the connection URI (e.g., `neo4j+s://xxx.databases.neo4j.io`)
   - Save the generated password securely
   - Default username is `neo4j`

3. **Configure Environment Variables**
   ```bash
   # Add to your .env file
   NEO4J_URI=neo4j+s://your-instance.databases.neo4j.io
   NEO4J_USER=neo4j
   NEO4J_PASSWORD=your-generated-password
   MIGRATE_ON_STARTUP=true
   ```

### 2. Install Neo4j Migrations CLI

The project uses the official [neo4j-migrations](https://neo4j.com/docs/neo4j-migrations/current/) CLI tool.

#### Option A: Using Docker (Recommended)
```bash
# No installation needed - migrations run in Docker container
docker run --rm neo4j/neo4j-migrations:latest --version
```

#### Option B: Local Installation
```bash
# macOS with Homebrew
brew install neo4j-migrations

# Linux/Windows - Download from GitHub releases
# https://github.com/michael-simons/neo4j-migrations/releases
```

### 3. Run Migrations

```bash
# From project root
cd backend
python run_neo4j_migrations.py

# Or with options
python run_neo4j_migrations.py --dry-run  # Show what would be executed
python run_neo4j_migrations.py --info     # Show migration status
python run_neo4j_migrations.py --force    # Force execution
```

## 📁 Migration Structure

```
backend/neo4j/migrations/
├── V001__core_schema.cypher      # Initial schema with constraints
├── V002__circle_counts.cypher    # Circle member count triggers
├── V003__bible_labels.cypher     # BC_ label prefix migration
├── V004__bible_rbac.cypher       # RBAC isolation setup
└── V005__your_migration.cypher   # Your future migrations
```

### Migration Naming Convention

- **Format**: `V{version}__{description}.cypher`
- **Version**: Zero-padded numbers (001, 002, 003, etc.)
- **Description**: Snake_case description
- **Examples**:
  - `V001__core_schema.cypher`
  - `V002__circle_counts.cypher`
  - `V003__reading_progress_indexes.cypher`

## 🔧 Current Schema

### Node Types

#### BC_User (Bible Companion User)
```cypher
(:BC_User {
  id: string,           // Unique identifier
  email: string,        // User email
  firstName: string,    // First name
  lastName: string,     // Last name
  isActive: boolean,    // Account status
  createdAt: datetime,  // Creation timestamp
  updatedAt: datetime   // Last update timestamp
})
```

#### BC_Verse (Bible Verse)
```cypher
(:BC_Verse {
  lang: string,         // Language code (en, rw, sw, fr)
  ver: string,          // Version/translation (NIV, ESV, etc.)
  book: string,         // Book name
  chapter: integer,     // Chapter number
  verse: integer,       // Verse number
  text: string,         // Verse text content
  createdAt: datetime,
  updatedAt: datetime
})
```

#### BC_Circle (Prayer Circle)
```cypher
(:BC_Circle {
  id: string,           // Unique identifier
  name: string,         // Circle name
  ownerId: string,      // Owner user ID
  visibility: string,   // public, private, invite-only
  memberCount: integer, // Auto-maintained count
  createdAt: datetime,
  updatedAt: datetime
})
```

### Relationship Types

#### MEMBER_OF
```cypher
(user:BC_User)-[:MEMBER_OF {
  id: string,           // Unique relationship ID
  joinedAt: datetime,   // When user joined
  role: string          // member, admin, owner
}]->(circle:BC_Circle)
```

#### READ
```cypher
(user:BC_User)-[:READ {
  id: string,           // Unique relationship ID
  readAt: datetime,     // When verse was read
  duration: integer,    // Reading time in seconds
  notes: string         // Optional reading notes
}]->(verse:BC_Verse)
```

### Constraints & Indexes

- **Unique Constraints**:
  - `BC_User.id`
  - `BC_Circle.id`
  - `(BC_Verse.lang, BC_Verse.ver, BC_Verse.book, BC_Verse.chapter, BC_Verse.verse)`
  - `MEMBER_OF.id`
  - `READ.id`

- **Indexes**:
  - Full-text search on `BC_Verse.text` (BC_VerseText index)
  - `BC_User.email`
  - `BC_Verse.book`
  - `(BC_Verse.lang, BC_Verse.ver)`
  - `BC_Circle.name`
  - `BC_Circle.visibility`
  - `BC_Circle.ownerId`

## ✍️ Creating New Migrations

### 1. Create Migration File

```bash
# Create new migration file
touch backend/neo4j/migrations/V003__your_feature.cypher
```

### 2. Write Migration

```cypher
// V003__reading_streaks.cypher
// Add reading streak tracking

// Create index for reading streak queries
CREATE INDEX user_reading_streak_index IF NOT EXISTS
FOR (u:User) ON (u.currentStreak, u.longestStreak);

// Add streak properties to existing users
MATCH (u:User)
SET u.currentStreak = 0,
    u.longestStreak = 0,
    u.lastReadDate = null,
    u.updatedAt = datetime();
```

### 3. Test Migration

```bash
# Test with dry-run first
python run_neo4j_migrations.py --dry-run

# Run the migration
python run_neo4j_migrations.py
```

## 🔍 Troubleshooting

### Common Issues

1. **Connection Failed**
   ```
   Error: Unable to connect to Neo4j
   ```
   - Check `NEO4J_URI`, `NEO4J_USER`, `NEO4J_PASSWORD`
   - Verify Neo4j Aura instance is running
   - Check firewall/network settings

2. **Migration Failed**
   ```
   Error: Constraint already exists
   ```
   - Use `IF NOT EXISTS` in constraint creation
   - Check migration history: `python run_neo4j_migrations.py --info`

3. **APOC Procedures Not Available**
   ```
   Error: Unknown procedure: apoc.trigger.add
   ```
   - APOC is pre-installed in Neo4j Aura
   - For local Neo4j, install APOC plugin

### Useful Commands

```bash
# Check migration status
python run_neo4j_migrations.py --info

# Validate migrations without running
python run_neo4j_migrations.py --dry-run

# View schema in Neo4j Browser
:schema

# Check constraints
SHOW CONSTRAINTS

# Check indexes
SHOW INDEXES

# View migration history
MATCH (m:__Neo4jMigration) RETURN m ORDER BY m.version
```

## 🔗 Integration with FastAPI

The migration runner can be integrated with the FastAPI startup process:

```python
# In backend/main.py lifespan function
from run_neo4j_migrations import Neo4jMigrationRunner, should_migrate_on_startup

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    if should_migrate_on_startup():
        runner = Neo4jMigrationRunner()
        success = runner.run_migrations()
        if not success:
            raise Exception("Neo4j migrations failed")
    
    yield
    # Shutdown
```

## 🏢 Multi-Tenant Architecture & RBAC

### Label-Prefix Isolation Strategy

Starting with V003 migrations, Bible Companion implements a label-prefix isolation strategy for multi-tenant Neo4j deployments:

#### Rationale
- **Shared Instance**: Multiple applications (Bible Companion, AiLex, etc.) share a single Neo4j Aura-Pro instance
- **Data Isolation**: Each application uses prefixed labels (`BC_*`, `AiLex_*`) to ensure complete data separation
- **Cost Efficiency**: Avoids the need for separate Neo4j instances per application
- **Security**: RBAC ensures applications cannot access each other's data

#### Label Prefixes
- **Bible Companion**: `BC_User`, `BC_Verse`, `BC_Circle`
- **AiLex**: `AiLex_Doc`, `AiLex_Knowledge` (example)
- **Future Apps**: `AppName_*` pattern

#### RBAC Implementation
```cypher
-- Application-specific role with restricted access
CREATE ROLE bible_app;
GRANT MATCH, WRITE ON GRAPH ELEMENTS BC_* TO bible_app;
DENY MATCH ON GRAPH ELEMENTS AiLex_* TO bible_app;

-- Dedicated application user
CREATE USER bc_app SET PASSWORD 'secure_password';
GRANT ROLE bible_app TO bc_app;
```

#### Environment Configuration
```bash
# Use application-specific credentials
NEO4J_USER=bc_app
NEO4J_PASSWORD=your_secure_password
BC_APP_PW=your_secure_password  # For migrations
```

### Future Migration Path

When ready to move to a dedicated instance:

```bash
# Export only Bible Companion data
neo4j-admin dump --filter "BC_*" --to=bible_companion.dump

# Import to new dedicated instance
neo4j-admin load --from=bible_companion.dump --database=neo4j

# Update labels back to standard format
MATCH (n:BC_User) SET n:User REMOVE n:BC_User;
MATCH (n:BC_Verse) SET n:Verse REMOVE n:BC_Verse;
MATCH (n:BC_Circle) SET n:Circle REMOVE n:BC_Circle;
```

This approach provides:
- ✅ **Zero data loss** during migration
- ✅ **Complete isolation** between applications
- ✅ **Cost optimization** through shared infrastructure
- ✅ **Easy migration path** to dedicated instances

## 📚 Resources

- [Neo4j Migrations Documentation](https://neo4j.com/docs/neo4j-migrations/current/)
- [Neo4j Aura Documentation](https://neo4j.com/docs/aura/)
- [Cypher Query Language](https://neo4j.com/docs/cypher-manual/current/)
- [APOC Procedures](https://neo4j.com/docs/apoc/current/)
- [Neo4j RBAC Documentation](https://neo4j.com/docs/operations-manual/current/authentication-authorization/)
