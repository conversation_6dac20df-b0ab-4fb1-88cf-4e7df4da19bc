import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { GiftedChat, IMessage, InputToolbar, Bubble, Send } from 'react-native-gifted-chat';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../../src/hooks/useTheme';
import { useAuth } from '../../../src/contexts/AuthContext';
import { useChatStore } from '../../../src/stores/chatStore';
import { apiService, ApiError } from '../../../src/services/api';
import { Circle } from '../../../src/types/circle';
import { VoiceButton } from '../../../src/components/voice/VoiceButton';
import { TTSButton } from '../../../src/components/voice/TTSButton';

const CircleChatScreen = (): JSX.Element => {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { user, mongoUser } = useAuth();
  const { id: circleId } = useLocalSearchParams<{ id: string }>();

  const {
    messages,
    isConnected,
    isConnecting,
    connectionError,
    connectToCircle,
    disconnectFromCircle,
    sendMessage,
    sendTyping,
    setCurrentCircle,
  } = useChatStore();

  const [circle, setCircle] = useState<Circle | null>(null);
  const [loading, setLoading] = useState(true);
  const [isTyping, setIsTyping] = useState(false);

  const circleMessages = circleId ? messages[circleId] || [] : [];

  // Convert our ChatMessage format to GiftedChat IMessage format
  const giftedChatMessages: IMessage[] = circleMessages.map(msg => ({
    _id: msg._id,
    text: msg.text,
    createdAt: msg.createdAt,
    user: {
      _id: msg.user._id,
      name: msg.user.name,
      avatar: msg.user.avatar,
    },
    pending: msg.pending,
    sent: msg.sent,
    received: msg.received,
  }));

  const loadCircle = useCallback(async () => {
    if (!circleId) return;

    try {
      const circleData = await apiService.getCircle(circleId);
      setCircle(circleData);
    } catch (err) {
      console.error('Failed to load circle:', err);
      const errorMessage = err instanceof ApiError
        ? err.message
        : t('errors.circleNotFound');

      Alert.alert(t('error'), errorMessage);
    } finally {
      setLoading(false);
    }
  }, [circleId, t]);

  const connectToChat = useCallback(async () => {
    if (!circleId) return;

    try {
      setCurrentCircle(circleId);
      await connectToCircle(circleId);
    } catch (err) {
      console.error('Failed to connect to chat:', err);
      Alert.alert(t('error'), t('errors.failedToConnect'));
    }
  }, [circleId, connectToCircle, setCurrentCircle, t]);

  useEffect(() => {
    if (circleId) {
      loadCircle();
      connectToChat();
    }

    return () => {
      disconnectFromCircle();
    };
  }, [circleId, loadCircle, connectToChat, disconnectFromCircle]);

  const handleSend = useCallback((newMessages: IMessage[] = []) => {
    if (newMessages.length === 0) return;

    const [message] = newMessages;
    try {
      sendMessage(message.text);
    } catch (err) {
      console.error('Failed to send message:', err);
      Alert.alert(t('error'), t('errors.failedToSendMessage'));
    }
  }, [sendMessage, t]);

  const handleInputTextChanged = useCallback((text: string) => {
    const shouldShowTyping = text.length > 0;

    if (shouldShowTyping !== isTyping) {
      setIsTyping(shouldShowTyping);
      sendTyping(shouldShowTyping);
    }
  }, [isTyping, sendTyping]);

  const handleVoiceTranscript = useCallback((transcript: string) => {
    if (transcript.trim()) {
      // Create a message from voice transcript
      const message: IMessage = {
        _id: Math.random().toString(),
        text: transcript,
        createdAt: new Date(),
        user: {
          _id: mongoUser?._id || user?.uid || 'unknown',
          name: mongoUser?.profile.firstName
            ? `${mongoUser.profile.firstName} ${mongoUser.profile.lastName}`.trim()
            : user?.displayName || 'You',
        },
      };
      handleSend([message]);
    }
  }, [handleSend, mongoUser, user]);

  const renderInputToolbar = (props: any) => (
    <InputToolbar
      {...props}
      containerStyle={[
        styles.inputToolbar,
        { backgroundColor: theme.colors.surface, borderTopColor: theme.colors.border },
      ]}
      textInputStyle={{ color: theme.colors.text }}
      placeholderTextColor={theme.colors.textSecondary}
    />
  );

  const renderBubble = (props: any) => {
    const isAIMessage = props.currentMessage?.user?._id === 'ai-assistant';

    return (
      <View>
        <Bubble
          {...props}
          wrapperStyle={{
            right: {
              backgroundColor: theme.colors.primary,
            },
            left: {
              backgroundColor: theme.colors.surface,
            },
          }}
          textStyle={{
            right: {
              color: theme.colors.surface,
            },
            left: {
              color: theme.colors.text,
            },
          }}
        />
        {isAIMessage && (
          <View style={styles.ttsButtonContainer}>
            <TTSButton
              text={props.currentMessage.text}
              size="small"
            />
          </View>
        )}
      </View>
    );
  };

  const renderSend = (props: any) => (
    <Send
      {...props}
      containerStyle={styles.sendContainer}
      textStyle={{ color: theme.colors.primary, fontWeight: 'bold' }}
    />
  );

  const renderConnectionStatus = () => {
    if (isConnecting) {
      return (
        <View style={[styles.statusBar, { backgroundColor: theme.colors.warning }]}>
          <ActivityIndicator size="small" color={theme.colors.surface} />
          <Text style={[styles.statusText, { color: theme.colors.surface }]}>
            {t('connecting')}
          </Text>
        </View>
      );
    }

    if (!isConnected) {
      return (
        <View style={[styles.statusBar, { backgroundColor: theme.colors.error }]}>
          <Text style={[styles.statusText, { color: theme.colors.surface }]}>
            {connectionError || t('disconnected')}
          </Text>
        </View>
      );
    }

    return null;
  };

  if (loading) {
    return (
      <View
        style={[
          styles.container,
          styles.centered,
          { backgroundColor: theme.colors.background },
        ]}
      >
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
          {t('loading')}
        </Text>
      </View>
    );
  }

  if (!circle) {
    return (
      <View
        style={[
          styles.container,
          styles.centered,
          { backgroundColor: theme.colors.background },
        ]}
      >
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {t('errors.circleNotFound')}
        </Text>
      </View>
    );
  }

  const currentUser = {
    _id: mongoUser?._id || user?.uid || 'unknown',
    name: mongoUser?.profile.firstName
      ? `${mongoUser.profile.firstName} ${mongoUser.profile.lastName}`.trim()
      : user?.displayName || 'You',
    avatar: mongoUser?.profile.avatar || user?.photoURL || undefined,
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {renderConnectionStatus()}

      <GiftedChat
        messages={giftedChatMessages}
        onSend={handleSend}
        onInputTextChanged={handleInputTextChanged}
        user={currentUser}
        renderInputToolbar={renderInputToolbar}
        renderBubble={renderBubble}
        renderSend={renderSend}
        placeholder={t('typeMessage')}
        showUserAvatar
        showAvatarForEveryMessage={false}
        renderUsernameOnMessage
        scrollToBottom
        scrollToBottomComponent={() => (
          <View style={[styles.scrollToBottomButton, { backgroundColor: theme.colors.primary }]}>
            <Text style={[styles.scrollToBottomText, { color: theme.colors.surface }]}>↓</Text>
          </View>
        )}
        messagesContainerStyle={{ backgroundColor: theme.colors.background }}
        textInputProps={{
          style: { color: theme.colors.text },
          placeholderTextColor: theme.colors.textSecondary,
        }}
      />

      <View style={[styles.voiceContainer, { backgroundColor: theme.colors.background }]}>
        <VoiceButton
          onTranscript={handleVoiceTranscript}
          disabled={!isConnected}
          size="medium"
        />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  statusBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  inputToolbar: {
    borderTopWidth: 1,
    paddingHorizontal: 8,
  },
  sendContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  scrollToBottomButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    marginRight: 8,
  },
  scrollToBottomText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  voiceContainer: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  ttsButtonContainer: {
    position: 'absolute',
    right: 8,
    bottom: 4,
  },
});

export default CircleChatScreen;
