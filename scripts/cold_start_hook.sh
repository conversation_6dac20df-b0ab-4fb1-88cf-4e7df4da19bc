#!/usr/bin/env bash
#
# Fly.io Machine Cold Start Hook
#
# This script is executed when a Fly.io machine starts up from a cold state.
# It records the cold start event by pushing a metric to the Prometheus pushgateway.
#
# Usage: This script should be called during machine startup, typically from
# the Dockerfile or as part of the application initialization process.

set -euo pipefail

# Configuration
METRICS_URL="${METRICS_URL:-http://localhost:9091/metrics/job/gpu}"
METRIC_NAME="gpu_cold_start_total"
METRIC_VALUE="1"

# Log the cold start event
echo "$(date -Iseconds) [INFO] GPU machine cold start detected"

# Push metric to Prometheus pushgateway
# This assumes a pushgateway is running on port 9091
if curl -f -X POST "$METRICS_URL" \
    --data-binary "${METRIC_NAME} ${METRIC_VALUE}" \
    --connect-timeout 5 \
    --max-time 10 \
    --silent \
    --show-error; then
    echo "$(date -Iseconds) [INFO] Cold start metric pushed successfully"
else
    echo "$(date -Iseconds) [WARN] Failed to push cold start metric (non-critical)"
fi

# Optional: Send to application metrics endpoint
APP_METRICS_URL="${APP_METRICS_URL:-http://localhost:8000/metrics/cold-start}"
if [[ -n "${APP_METRICS_URL}" ]]; then
    if curl -f -X POST "$APP_METRICS_URL" \
        --header "Content-Type: application/json" \
        --data '{"event": "cold_start", "timestamp": "'$(date -Iseconds)'"}' \
        --connect-timeout 5 \
        --max-time 10 \
        --silent \
        --show-error; then
        echo "$(date -Iseconds) [INFO] Cold start event sent to application"
    else
        echo "$(date -Iseconds) [WARN] Failed to send cold start event to application (non-critical)"
    fi
fi

echo "$(date -Iseconds) [INFO] Cold start hook completed"
